import pandas as pd
from io import BytesIO
from flask import send_file
from models import Student, Class, Grade, Subject, Period, StudentGrade

def export_students_to_excel(class_id=None):
    """تصدير بيانات الطلاب إلى ملف Excel"""
    
    # استعلام الطلاب
    if class_id:
        students = Student.query.filter_by(class_id=class_id).all()
    else:
        students = Student.query.all()
    
    # إنشاء قائمة البيانات
    data = []
    for student in students:
        data.append({
            'الرقم': student.id,
            'الاسم': student.name,
            'الرقم الوطني': student.national_id,
            'الفصل': student.class_.name,
            'الصف الدراسي': student.class_.grade.name,
            'المسار': student.class_.grade.track,
            'الرقم السري': student.secret_number,
            'رقم الجلوس': student.seat_number
        })
    
    # إنشاء DataFrame
    df = pd.DataFrame(data)
    
    # إنشاء ملف Excel في الذاكرة
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='الطلاب')
    
    output.seek(0)
    
    # إرسال الملف
    return send_file(
        output,
        as_attachment=True,
        download_name='students.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

def export_grades_to_excel(class_id=None, period_id=None):
    """تصدير بيانات الدرجات إلى ملف Excel"""
    
    # استعلام الطلاب
    if class_id:
        students = Student.query.filter_by(class_id=class_id).all()
    else:
        students = Student.query.all()
    
    # استعلام الفترات
    if period_id:
        periods = [Period.query.get(period_id)]
    else:
        periods = Period.query.all()
    
    # إنشاء ملف Excel في الذاكرة
    output = BytesIO()
    
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        # لكل فترة، إنشاء ورقة عمل
        for period in periods:
            data = []
            
            for student in students:
                # الحصول على درجات الطالب في هذه الفترة
                grades = StudentGrade.query.filter_by(
                    student_id=student.id,
                    period_id=period.id,
                    is_second_chance=False
                ).all()
                
                # الحصول على درجات الدور الثاني
                second_chance_grades = StudentGrade.query.filter_by(
                    student_id=student.id,
                    period_id=period.id,
                    is_second_chance=True
                ).all()
                
                # إنشاء قاموس لدرجات الدور الثاني
                second_chance_dict = {g.subject_id: g for g in second_chance_grades}
                
                student_data = {
                    'الرقم': student.id,
                    'الاسم': student.name,
                    'الرقم الوطني': student.national_id,
                    'الفصل': student.class_.name,
                    'الصف الدراسي': student.class_.grade.name,
                    'المسار': student.class_.grade.track
                }
                
                # إضافة درجات المواد
                for grade in grades:
                    subject = Subject.query.get(grade.subject_id)
                    
                    # التحقق من وجود درجة الدور الثاني
                    second_chance = second_chance_dict.get(grade.subject_id)
                    
                    # استخدام درجة الدور الثاني إذا كانت موجودة وكان الطالب ناجحًا فيها
                    final_grade = second_chance if second_chance and second_chance.is_passed else grade
                    
                    student_data[f'{subject.name} - أعمال الفصل'] = final_grade.classwork_mark
                    student_data[f'{subject.name} - الامتحان'] = final_grade.exam_mark
                    student_data[f'{subject.name} - المجموع'] = final_grade.total_mark * 100 if final_grade.total_mark else None
                    student_data[f'{subject.name} - النتيجة'] = 'ناجح' if final_grade.is_passed else 'راسب'
                
                data.append(student_data)
            
            # إنشاء DataFrame
            if data:
                df = pd.DataFrame(data)
                df.to_excel(writer, index=False, sheet_name=period.name)
    
    output.seek(0)
    
    # إرسال الملف
    return send_file(
        output,
        as_attachment=True,
        download_name='grades.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

def export_numbers_to_excel(class_id=None):
    """تصدير الأرقام السرية وأرقام الجلوس إلى ملف Excel"""
    
    # استعلام الطلاب
    if class_id:
        students = Student.query.filter_by(class_id=class_id).all()
    else:
        students = Student.query.all()
    
    # إنشاء قائمة البيانات
    data = []
    for student in students:
        data.append({
            'الرقم': student.id,
            'الاسم': student.name,
            'الرقم الوطني': student.national_id,
            'الفصل': student.class_.name,
            'الصف الدراسي': student.class_.grade.name,
            'المسار': student.class_.grade.track,
            'الرقم السري': student.secret_number,
            'رقم الجلوس': student.seat_number
        })
    
    # إنشاء DataFrame
    df = pd.DataFrame(data)
    
    # إنشاء ملف Excel في الذاكرة
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='الأرقام')
    
    output.seek(0)
    
    # إرسال الملف
    return send_file(
        output,
        as_attachment=True,
        download_name='numbers.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

def export_student_grades_report(class_id, subject_id, period_id):
    """تصدير تقرير درجات الطلاب إلى ملف Excel"""
    
    # استعلام الطلاب
    students = Student.query.filter_by(class_id=class_id).all()
    subject = Subject.query.get(subject_id)
    period = Period.query.get(period_id)
    class_obj = Class.query.get(class_id)
    
    # إنشاء قائمة البيانات
    data = []
    for student in students:
        # البحث عن درجة الطالب إن وجدت
        grade = StudentGrade.query.filter_by(
            student_id=student.id,
            subject_id=subject_id,
            period_id=period_id,
            is_second_chance=False
        ).first()
        
        # البحث عن درجة الدور الثاني إن وجدت
        second_chance = StudentGrade.query.filter_by(
            student_id=student.id,
            subject_id=subject_id,
            period_id=period_id,
            is_second_chance=True
        ).first()
        
        # استخدام درجة الدور الثاني إذا كانت موجودة وكان الطالب ناجحًا فيها
        final_grade = second_chance if second_chance and second_chance.is_passed else grade
        
        data.append({
            'الرقم': student.id,
            'الاسم': student.name,
            'الرقم الوطني': student.national_id,
            'أعمال الفصل': final_grade.classwork_mark if final_grade else None,
            'الامتحان': final_grade.exam_mark if final_grade else None,
            'المجموع': final_grade.total_mark * 100 if final_grade and final_grade.total_mark else None,
            'النتيجة': 'ناجح' if final_grade and final_grade.is_passed else 'راسب'
        })
    
    # إنشاء DataFrame
    df = pd.DataFrame(data)
    
    # إنشاء ملف Excel في الذاكرة
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        # إضافة معلومات التقرير
        workbook = writer.book
        worksheet = workbook.create_sheet('تقرير درجات الطلاب')
        
        # إضافة العنوان
        worksheet['A1'] = 'تقرير درجات الطلاب'
        worksheet['A2'] = f'الفصل: {class_obj.name} - {class_obj.grade.name} ({class_obj.grade.track})'
        worksheet['A3'] = f'المادة: {subject.name}'
        worksheet['A4'] = f'الفترة: {period.name} - {period.academic_year}'
        
        # إضافة البيانات
        df.to_excel(writer, index=False, sheet_name='تقرير درجات الطلاب', startrow=6)
    
    output.seek(0)
    
    # إرسال الملف
    return send_file(
        output,
        as_attachment=True,
        download_name=f'student_grades_{class_obj.name}_{subject.name}_{period.name}.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

def export_final_results_report(class_id, period_id):
    """تصدير تقرير النتائج النهائية إلى ملف Excel"""
    
    # استعلام الطلاب
    students = Student.query.filter_by(class_id=class_id).all()
    subjects = Subject.query.join(Grade).join(Class).filter(Class.id == class_id).all()
    period = Period.query.get(period_id)
    class_obj = Class.query.get(class_id)
    
    # إنشاء قائمة البيانات
    data = []
    for student in students:
        student_data = {
            'الرقم': student.id,
            'الاسم': student.name,
            'الرقم الوطني': student.national_id
        }
        
        total_marks = 0
        passed_subjects = 0
        
        for subject in subjects:
            # البحث عن درجة الطالب إن وجدت
            grade = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject.id,
                period_id=period_id,
                is_second_chance=False
            ).first()
            
            # البحث عن درجة الدور الثاني إن وجدت
            second_chance = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject.id,
                period_id=period_id,
                is_second_chance=True
            ).first()
            
            # استخدام درجة الدور الثاني إذا كانت موجودة وكان الطالب ناجحًا فيها
            final_grade = second_chance if second_chance and second_chance.is_passed else grade
            
            mark = final_grade.total_mark * 100 if final_grade and final_grade.total_mark else 0
            is_passed = final_grade and final_grade.is_passed
            
            student_data[subject.name] = mark
            
            total_marks += mark
            if is_passed:
                passed_subjects += 1
        
        # حساب المجموع والنسبة المئوية
        student_data['المجموع'] = total_marks
        student_data['النسبة المئوية'] = total_marks / len(subjects) if subjects else 0
        student_data['النتيجة'] = 'ناجح' if passed_subjects == len(subjects) else 'راسب'
        
        data.append(student_data)
    
    # إنشاء DataFrame
    df = pd.DataFrame(data)
    
    # إنشاء ملف Excel في الذاكرة
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        # إضافة معلومات التقرير
        workbook = writer.book
        worksheet = workbook.create_sheet('تقرير النتائج النهائية')
        
        # إضافة العنوان
        worksheet['A1'] = 'تقرير النتائج النهائية'
        worksheet['A2'] = f'الفصل: {class_obj.name} - {class_obj.grade.name} ({class_obj.grade.track})'
        worksheet['A3'] = f'الفترة: {period.name} - {period.academic_year}'
        
        # إضافة البيانات
        df.to_excel(writer, index=False, sheet_name='تقرير النتائج النهائية', startrow=5)
    
    output.seek(0)
    
    # إرسال الملف
    return send_file(
        output,
        as_attachment=True,
        download_name=f'final_results_{class_obj.name}_{period.name}.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
