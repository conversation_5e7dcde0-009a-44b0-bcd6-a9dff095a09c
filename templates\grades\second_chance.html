{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <h1 class="text-2xl font-bold mb-6 text-blue-600">الدور الثاني - الطلاب المتعثرين</h1>

    <!-- نموذج اختيار الفصل والمادة والفترة -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 class="text-lg font-semibold mb-4">اختر الفصل والمادة والفترة</h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="class-select" class="block text-gray-700 font-medium mb-2">الفصل</label>
                <select id="class-select" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="" disabled selected>اختر الفصل</option>
                    {% for class in classes %}
                    <option value="{{ class.id }}">{{ class.name }} - {{ class.grade.name }} ({{ class.grade.track }})</option>
                    {% endfor %}
                </select>
            </div>

            <div>
                <label for="subject-select" class="block text-gray-700 font-medium mb-2">المادة</label>
                <select id="subject-select" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled>
                    <option value="" disabled selected>اختر المادة</option>
                    {% for subject in subjects %}
                    <option value="{{ subject.id }}" data-grade-id="{{ subject.grade_id }}">{{ subject.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <div>
                <label for="period-select" class="block text-gray-700 font-medium mb-2">الفترة</label>
                <select id="period-select" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled>
                    <option value="" disabled selected>اختر الفترة</option>
                    {% for period in periods %}
                    <option value="{{ period.id }}">{{ period.name }} - {{ period.academic_year }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <div class="mt-4 text-center">
            <button id="load-students-btn" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors" disabled>
                <i class="fas fa-search ml-1"></i> عرض الطلاب المتعثرين
            </button>
        </div>
    </div>

    <!-- جدول رصد درجات الدور الثاني -->
    <div id="grades-container" class="hidden">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">رصد درجات الدور الثاني</h2>
            <div class="text-sm text-gray-600">
                <span class="text-red-500 font-semibold">ملاحظة:</span> يتم نقل درجة أعمال الفصل كما هي من الدور الأول
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="py-3 px-4 border-b text-right">#</th>
                        <th class="py-3 px-4 border-b text-right">الاسم</th>
                        <th class="py-3 px-4 border-b text-right">الرقم الوطني</th>
                        <th class="py-3 px-4 border-b text-right">أعمال الفصل (40%)</th>
                        <th class="py-3 px-4 border-b text-right">امتحان الدور الثاني (60%)</th>
                        <th class="py-3 px-4 border-b text-right">المجموع</th>
                        <th class="py-3 px-4 border-b text-right">النتيجة</th>
                    </tr>
                </thead>
                <tbody id="grades-table-body">
                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                </tbody>
            </table>
        </div>

        <div class="mt-6 flex justify-end">
            <button id="save-grades-btn" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-6 rounded transition-colors">
                <i class="fas fa-save ml-1"></i> حفظ درجات الدور الثاني
            </button>
        </div>
    </div>

    <!-- رسالة عدم وجود طلاب متعثرين -->
    <div id="no-students-message" class="hidden text-center py-10 text-gray-500">
        <i class="fas fa-check-circle text-5xl mb-4 text-green-300"></i>
        <p>لا يوجد طلاب متعثرين في هذه المادة</p>
    </div>

    <!-- مؤشر التحميل -->
    <div id="loading-indicator" class="hidden text-center py-10">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">جاري تحميل البيانات...</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // العناصر
    const classSelect = document.getElementById('class-select');
    const subjectSelect = document.getElementById('subject-select');
    const periodSelect = document.getElementById('period-select');
    const loadStudentsBtn = document.getElementById('load-students-btn');
    const gradesContainer = document.getElementById('grades-container');
    const noStudentsMessage = document.getElementById('no-students-message');
    const loadingIndicator = document.getElementById('loading-indicator');
    const gradesTableBody = document.getElementById('grades-table-body');
    const saveGradesBtn = document.getElementById('save-grades-btn');

    // تفعيل/تعطيل الحقول
    classSelect.addEventListener('change', function() {
        const selectedClass = this.options[this.selectedIndex];

        if (selectedClass && selectedClass.value) {
            // تفعيل حقل المادة
            subjectSelect.disabled = false;

            // تحديث قائمة المواد بناءً على الصف الدراسي
            const classId = selectedClass.value;

            // الحصول على الصف الدراسي (Grade) للفصل المحدد
            fetch(`/api/class/${classId}/grade`)
                .then(response => response.json())
                .then(data => {
                    const gradeId = data.grade_id;

                    // تصفية المواد حسب الصف الدراسي
                    Array.from(subjectSelect.options).forEach(option => {
                        if (option.disabled) return;

                        const optionGradeId = option.dataset.gradeId;
                        option.style.display = (optionGradeId == gradeId) ? '' : 'none';
                    });
                });
        } else {
            subjectSelect.disabled = true;
            periodSelect.disabled = true;
            loadStudentsBtn.disabled = true;
        }

        // إعادة تعيين الاختيارات
        subjectSelect.selectedIndex = 0;
        periodSelect.selectedIndex = 0;
        periodSelect.disabled = true;
        loadStudentsBtn.disabled = true;
    });

    subjectSelect.addEventListener('change', function() {
        if (this.value) {
            periodSelect.disabled = false;
        } else {
            periodSelect.disabled = true;
            loadStudentsBtn.disabled = true;
        }

        // إعادة تعيين الاختيارات
        periodSelect.selectedIndex = 0;
        loadStudentsBtn.disabled = true;
    });

    periodSelect.addEventListener('change', function() {
        loadStudentsBtn.disabled = !this.value;
    });

    // تحميل الطلاب المتعثرين
    loadStudentsBtn.addEventListener('click', function() {
        const classId = classSelect.value;
        const subjectId = subjectSelect.value;
        const periodId = periodSelect.value;

        if (!classId || !subjectId || !periodId) {
            alert('يرجى اختيار الفصل والمادة والفترة');
            return;
        }

        // إظهار مؤشر التحميل
        gradesContainer.classList.add('hidden');
        noStudentsMessage.classList.add('hidden');
        loadingIndicator.classList.remove('hidden');

        // طلب البيانات من الخادم
        fetch(`/second-chance/students?class_id=${classId}&subject_id=${subjectId}&period_id=${periodId}`)
            .then(response => response.json())
            .then(data => {
                // إخفاء مؤشر التحميل
                loadingIndicator.classList.add('hidden');

                if (data.students && data.students.length > 0) {
                    // حفظ الدرجة الكلية للمادة في متغير عام
                    window.subjectTotalMark = data.subject.total_mark;

                    // ملء جدول الدرجات
                    gradesTableBody.innerHTML = '';

                    data.students.forEach((student, index) => {
                        const row = document.createElement('tr');
                        row.className = 'hover:bg-gray-50';
                        row.dataset.studentId = student.id;

                        row.innerHTML = `
                            <td class="py-3 px-4 border-b">${index + 1}</td>
                            <td class="py-3 px-4 border-b">${student.name}</td>
                            <td class="py-3 px-4 border-b">${student.national_id}</td>
                            <td class="py-3 px-4 border-b">
                                <input type="number" class="classwork-mark w-20 border border-gray-300 rounded py-1 px-2" min="0" max="${data.subject.total_mark}" value="${student.classwork_mark || ''}" disabled>
                            </td>
                            <td class="py-3 px-4 border-b">
                                <input type="number" class="exam-mark w-20 border border-gray-300 rounded py-1 px-2" min="0" max="${data.subject.total_mark}" value="${student.exam_mark || ''}">
                            </td>
                            <td class="py-3 px-4 border-b total-mark">${student.total_mark ? (Number.isInteger(student.total_mark) ? student.total_mark : student.total_mark.toFixed(2)) : '-'}</td>
                            <td class="py-3 px-4 border-b result">
                                ${student.is_passed === true ? '<span class="text-green-600 font-medium">ناجح</span>' :
                                  student.is_passed === false ? '<span class="text-red-600 font-medium">راسب</span>' : '-'}
                            </td>
                        `;

                        gradesTableBody.appendChild(row);
                    });

                    // إظهار جدول الدرجات
                    gradesContainer.classList.remove('hidden');

                    // إضافة مستمعي الأحداث لحقول الإدخال
                    setupInputListeners();
                } else {
                    // إظهار رسالة عدم وجود طلاب متعثرين
                    noStudentsMessage.classList.remove('hidden');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                loadingIndicator.classList.add('hidden');
                alert('حدث خطأ أثناء تحميل البيانات');
            });
    });

    // إعداد مستمعي الأحداث لحقول الإدخال
    function setupInputListeners() {
        document.querySelectorAll('.exam-mark').forEach(input => {
            input.addEventListener('input', function() {
                // التحقق من صحة القيمة
                if (this.value < 0) this.value = 0;

                // الحصول على الصف
                const row = this.closest('tr');

                // الحصول على قيم الدرجات
                const classworkInput = row.querySelector('.classwork-mark');
                const examInput = row.querySelector('.exam-mark');
                const totalCell = row.querySelector('.total-mark');
                const resultCell = row.querySelector('.result');

                // الحصول على الدرجة الكلية للمادة من البيانات المستلمة من الخادم
                const subjectTotalMark = window.subjectTotalMark || 40;

                // حساب الدرجة الكلية للفصل (نصف الدرجة الكلية للمادة)
                const semesterTotalMark = subjectTotalMark / 2;

                // الحدود القصوى للدرجات
                const maxExam = semesterTotalMark * 0.6; // مثلاً: 24 درجة إذا كانت الدرجة الكلية للفصل 40

                // التحقق من درجة الامتحان إذا تم إدخالها
                if (this.value) {
                    const examMark = parseFloat(this.value);
                    if (examMark > maxExam) {
                        alert(`تنبيه: درجة الامتحان لا يمكن أن تتجاوز ${maxExam.toFixed(1)} (60% من الدرجة الكلية للفصل ${semesterTotalMark.toFixed(1)})`);
                        this.value = maxExam;
                    }
                }

                // التحقق من وجود قيم في كلا الحقلين لحساب المجموع
                if (classworkInput.value && examInput.value) {
                    // تحويل القيم إلى أرقام
                    const classworkMark = parseFloat(classworkInput.value);
                    const examMark = parseFloat(examInput.value);

                    // الحصول على الدرجة الكلية للمادة من البيانات المستلمة من الخادم
                    const subjectTotalMark = window.subjectTotalMark || 40;

                    try {
                        // حساب الدرجة الكلية للفصل (نصف الدرجة الكلية للمادة)
                        const semesterTotalMark = subjectTotalMark / 2;

                        // التأكد من أن درجة الأعمال لا تتجاوز 40% من الدرجة الكلية للفصل
                        const maxClasswork = semesterTotalMark * 0.4; // مثلاً: 16 درجة إذا كانت الدرجة الكلية للفصل 40

                        // إذا تجاوزت درجة الأعمال الحد الأقصى، نعرض تنبيهًا
                        if (classworkMark > maxClasswork) {
                            alert(`تنبيه: درجة أعمال الفصل لا يمكن أن تتجاوز ${maxClasswork.toFixed(1)} (40% من الدرجة الكلية للفصل ${semesterTotalMark.toFixed(1)})`);
                            classworkInput.value = maxClasswork;
                            return; // نتوقف عن المتابعة حتى يصحح المستخدم الدرجة
                        }

                        // التأكد من أن درجة الامتحان لا تتجاوز 60% من الدرجة الكلية للفصل
                        const maxExam = semesterTotalMark * 0.6; // مثلاً: 24 درجة إذا كانت الدرجة الكلية للفصل 40

                        // إذا تجاوزت درجة الامتحان الحد الأقصى، نعرض تنبيهًا
                        if (examMark > maxExam) {
                            alert(`تنبيه: درجة الامتحان لا يمكن أن تتجاوز ${maxExam.toFixed(1)} (60% من الدرجة الكلية للفصل ${semesterTotalMark.toFixed(1)})`);
                            examInput.value = maxExam;
                            return; // نتوقف عن المتابعة حتى يصحح المستخدم الدرجة
                        }

                        const validClassworkMark = classworkMark;
                        const validExamMark = examMark;

                        // التحقق من شرط الامتحان (40% من درجة الامتحان)
                        const minExamMark = maxExam * 0.4; // 9.6 درجة إذا كانت درجة الامتحان 24
                        const minExamRequirement = validExamMark >= minExamMark;

                        // حساب المجموع الكلي
                        const totalPoints = validClassworkMark + validExamMark;

                        // عرض المجموع (النقاط) فقط إذا تحقق شرط الامتحان
                        if (minExamRequirement) {
                            // تنسيق الرقم: إذا كان رقمًا صحيحًا، نعرضه بدون كسور عشرية
                            // وإذا كان يحتوي على كسور، نعرضه بكسرين عشريين
                            totalCell.textContent = Number.isInteger(totalPoints) ? totalPoints.toString() : totalPoints.toFixed(2);
                        } else {
                            totalCell.textContent = "-";
                        }

                        // تخزين القيم في خصائص مخصصة للصف
                        row.dataset.classworkMark = validClassworkMark;
                        row.dataset.examMark = validExamMark;
                        row.dataset.totalPoints = totalPoints;
                        row.dataset.minExamRequirement = minExamRequirement;
                    } catch (error) {
                        console.error("خطأ في حساب الدرجات:", error);
                        totalCell.textContent = "خطأ";
                    }

                    // ويجب أن يكون المجموع الكلي 50% على الأقل
                    const minTotalMark = semesterTotalMark * 0.5; // مثلاً: 20 درجة إذا كانت الدرجة الكلية للفصل 40
                    const minTotalRequirement = totalPoints >= minTotalMark;

                    // طباعة قيم التشخيص
                    console.log(`درجة الأعمال: ${classworkMark}, درجة الامتحان: ${examMark}, الدرجة الكلية للمادة: ${subjectTotalMark}`);
                    console.log(`الحد الأقصى للأعمال: ${maxClasswork}, الحد الأقصى للامتحان: ${maxExam}`);
                    console.log(`درجة الأعمال المعدلة: ${validClassworkMark}, درجة الامتحان المعدلة: ${validExamMark}`);
                    console.log(`المجموع: ${totalPoints}`);
                    console.log(`الحد الأدنى للامتحان: ${minExamMark}, الحد الأدنى للمجموع: ${minTotalMark}`);
                    console.log(`شرط الامتحان: ${row.dataset.minExamRequirement}, شرط المجموع: ${minTotalRequirement}`);

                    // الحصول على قيمة شرط الامتحان من خصائص الصف
                    const minExamRequirement = row.dataset.minExamRequirement === "true";
                    const isPassed = minExamRequirement && minTotalRequirement;

                    resultCell.innerHTML = isPassed ?
                        '<span class="text-green-600 font-medium">ناجح</span>' :
                        '<span class="text-red-600 font-medium">راسب</span>';
                } else {
                    // إعادة تعيين الخلايا
                    totalCell.textContent = '-';
                    resultCell.textContent = '-';
                }
            });
        });
    }

    // حفظ درجات الدور الثاني
    saveGradesBtn.addEventListener('click', function() {
        const subjectId = subjectSelect.value;
        const periodId = periodSelect.value;
        const subjectTotalMark = window.subjectTotalMark || 40;

        // التحقق من صحة البيانات قبل الإرسال
        let hasErrors = false;
        document.querySelectorAll('#grades-table-body tr').forEach(row => {
            const examInput = row.querySelector('.exam-mark');

            // حساب الدرجة الكلية للفصل (نصف الدرجة الكلية للمادة)
            const semesterTotalMark = subjectTotalMark / 2;

            // الحد الأقصى لدرجة الامتحان
            const maxExam = semesterTotalMark * 0.6; // مثلاً: 24 درجة إذا كانت الدرجة الكلية للفصل 40

            if (examInput.value && parseFloat(examInput.value) > maxExam) {
                examInput.classList.add('border-red-500');
                hasErrors = true;
                alert(`تنبيه: درجة الامتحان لا يمكن أن تتجاوز ${maxExam.toFixed(1)} (60% من الدرجة الكلية للفصل ${semesterTotalMark.toFixed(1)})`);
            } else {
                examInput.classList.remove('border-red-500');
            }
        });

        if (hasErrors) {
            // حساب الدرجة الكلية للفصل (نصف الدرجة الكلية للمادة)
            const semesterTotalMark = subjectTotalMark / 2;
            alert(`يوجد درجات تتجاوز الحد الأقصى المسموح به. يرجى تصحيح الدرجات قبل الحفظ.\n\nالحد الأقصى لدرجة الامتحان: ${(semesterTotalMark * 0.6).toFixed(1)} (60% من الدرجة الكلية للفصل ${semesterTotalMark.toFixed(1)})`);
            return;
        }

        // جمع بيانات الدرجات
        const grades = [];

        document.querySelectorAll('#grades-table-body tr').forEach(row => {
            const studentId = row.dataset.studentId;
            const examMark = row.querySelector('.exam-mark').value;

            if (examMark) {
                grades.push({
                    student_id: studentId,
                    exam_mark: examMark
                });
            }
        });

        // إرسال البيانات إلى الخادم
        fetch('/second-chance/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                subject_id: subjectId,
                period_id: periodId,
                grades: grades
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حفظ درجات الدور الثاني بنجاح');

                // إفراغ حقول الإدخال بعد الحفظ
                document.querySelectorAll('#grades-table-body tr').forEach(row => {
                    const examInput = row.querySelector('.exam-mark');
                    const totalCell = row.querySelector('.total-mark');
                    const resultCell = row.querySelector('.result');

                    if (examInput) examInput.value = '';
                    if (totalCell) totalCell.textContent = '-';
                    if (resultCell) resultCell.textContent = '-';
                });
            } else {
                alert('حدث خطأ أثناء حفظ الدرجات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حفظ الدرجات');
        });
    });
</script>
{% endblock %}
