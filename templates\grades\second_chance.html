{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <h1 class="text-2xl font-bold mb-6 text-blue-600">الدور الثاني - الطلاب المتعثرين</h1>

    <!-- نموذج اختيار الفصل والمادة -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 class="text-lg font-semibold mb-4">اختر الفصل والمادة</h2>
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <div class="flex items-center">
                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                <p class="text-blue-700 text-sm">
                    <strong>ملاحظة:</strong> سيتم عرض الطلاب الذين لهم دور ثان بناءً على نتائج الفصلين مجتمعين.
                    <span class="font-semibold">الأعمال المرحلة:</span> أعمال الفصل الأول + أعمال الفصل الثاني (40% × 2).
                    <span class="font-semibold">امتحان الدور الثاني:</span> يعادل مجموع امتحان الفصلين (60% × 2).
                </p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="class-select" class="block text-gray-700 font-medium mb-2">الفصل</label>
                <select id="class-select" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="" disabled selected>اختر الفصل</option>
                    {% for class in classes %}
                    <option value="{{ class.id }}">{{ class.name }} - {{ class.grade.name }} ({{ class.grade.track }})</option>
                    {% endfor %}
                </select>
            </div>

            <div>
                <label for="subject-select" class="block text-gray-700 font-medium mb-2">المادة</label>
                <select id="subject-select" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled>
                    <option value="" disabled selected>اختر المادة</option>
                    {% for subject in subjects %}
                    <option value="{{ subject.id }}" data-grade-id="{{ subject.grade_id }}">{{ subject.name }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <div class="mt-4 text-center">
            <button id="load-students-btn" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors" disabled>
                <i class="fas fa-search ml-1"></i> عرض الطلاب المتعثرين
            </button>
        </div>
    </div>

    <!-- معلومات الفصل والمادة -->
    <div id="info-container" class="hidden bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-xl font-bold text-gray-800 mb-2">
            <i class="fas fa-info-circle text-green-500 mr-2"></i>
            معلومات الفصل: <span id="class-name" class="text-blue-600"></span>
        </h2>
        <p class="text-lg text-gray-700 mb-2">
            <i class="fas fa-book text-green-500 mr-2"></i>
            المادة: <span id="subject-name" class="text-green-600 font-semibold"></span>
        </p>
    </div>

    <!-- جدول رصد درجات الدور الثاني -->
    <div id="grades-container" class="hidden">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">رصد درجات الدور الثاني</h2>
            <div class="text-sm text-gray-600">
                <span class="text-blue-500 font-semibold">ملاحظة:</span> الأعمال المرحلة من الفصلين - امتحان الدور الثاني يعادل مجموع امتحان الفصلين
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="py-3 px-4 border-b text-center">#</th>
                        <th class="py-3 px-4 border-b text-right">الاسم</th>
                        <th class="py-3 px-4 border-b text-center">الرقم السري</th>
                        <th class="py-3 px-4 border-b text-center">رقم الجلوس</th>
                        <th class="py-3 px-4 border-b text-center bg-yellow-50">الأعمال المرحلة (40%)</th>
                        <th class="py-3 px-4 border-b text-center bg-blue-50">امتحان الدور الثاني (60%)</th>
                        <th class="py-3 px-4 border-b text-center bg-green-50">المجموع</th>
                        <th class="py-3 px-4 border-b text-center bg-red-50">النتيجة</th>
                    </tr>
                </thead>
                <tbody id="grades-table-body">
                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                </tbody>
            </table>
        </div>

        <div class="mt-6 flex justify-end">
            <button id="save-grades-btn" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-6 rounded transition-colors">
                <i class="fas fa-save ml-1"></i> حفظ درجات الدور الثاني
            </button>
        </div>
    </div>

    <!-- رسالة عدم وجود طلاب متعثرين -->
    <div id="no-students-message" class="hidden text-center py-10 text-gray-500">
        <i class="fas fa-check-circle text-5xl mb-4 text-green-300"></i>
        <p>لا يوجد طلاب متعثرين في هذه المادة</p>
    </div>

    <!-- مؤشر التحميل -->
    <div id="loading-indicator" class="hidden text-center py-10">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">جاري تحميل البيانات...</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // العناصر
    const classSelect = document.getElementById('class-select');
    const subjectSelect = document.getElementById('subject-select');
    const loadStudentsBtn = document.getElementById('load-students-btn');
    const gradesContainer = document.getElementById('grades-container');
    const infoContainer = document.getElementById('info-container');
    const noStudentsMessage = document.getElementById('no-students-message');
    const loadingIndicator = document.getElementById('loading-indicator');
    const gradesTableBody = document.getElementById('grades-table-body');
    const saveGradesBtn = document.getElementById('save-grades-btn');
    const classNameSpan = document.getElementById('class-name');
    const subjectNameSpan = document.getElementById('subject-name');

    // تفعيل/تعطيل الحقول
    classSelect.addEventListener('change', function() {
        const selectedClass = this.options[this.selectedIndex];

        if (selectedClass && selectedClass.value) {
            // تفعيل حقل المادة
            subjectSelect.disabled = false;

            // تحديث قائمة المواد بناءً على الصف الدراسي
            const classId = selectedClass.value;

            // الحصول على الصف الدراسي (Grade) للفصل المحدد
            fetch(`/api/class/${classId}/grade`)
                .then(response => response.json())
                .then(data => {
                    const gradeId = data.grade_id;

                    // تصفية المواد حسب الصف الدراسي
                    Array.from(subjectSelect.options).forEach(option => {
                        if (option.disabled) return;

                        const optionGradeId = option.dataset.gradeId;
                        option.style.display = (optionGradeId == gradeId) ? '' : 'none';
                    });
                });
        } else {
            subjectSelect.disabled = true;
            loadStudentsBtn.disabled = true;
        }

        // إعادة تعيين الاختيارات
        subjectSelect.selectedIndex = 0;
        loadStudentsBtn.disabled = true;
    });

    subjectSelect.addEventListener('change', function() {
        if (this.value) {
            loadStudentsBtn.disabled = false;
        } else {
            loadStudentsBtn.disabled = true;
        }
    });

    // تحميل الطلاب المتعثرين
    loadStudentsBtn.addEventListener('click', function() {
        const classId = classSelect.value;
        const subjectId = subjectSelect.value;

        if (!classId || !subjectId) {
            alert('يرجى اختيار الفصل والمادة');
            return;
        }

        // إظهار مؤشر التحميل
        gradesContainer.classList.add('hidden');
        infoContainer.classList.add('hidden');
        noStudentsMessage.classList.add('hidden');
        loadingIndicator.classList.remove('hidden');

        // طلب البيانات من الخادم (النظام الجديد)
        console.log('Fetching data for class:', classId, 'subject:', subjectId);
        fetch(`/second-chance/students-new?class_id=${classId}&subject_id=${subjectId}`)
            .then(response => response.json())
            .then(data => {
                console.log('Received data:', data);
                // إخفاء مؤشر التحميل
                loadingIndicator.classList.add('hidden');

                if (data.students && data.students.length > 0) {
                    // حفظ الدرجة الكلية للمادة في متغير عام
                    window.subjectTotalMark = data.subject.total_mark;

                    // عرض معلومات الفصل والمادة
                    classNameSpan.textContent = data.class_name;
                    subjectNameSpan.textContent = data.subject.name;
                    infoContainer.classList.remove('hidden');

                    // تحديث عناوين الأعمدة بالدرجات الصحيحة
                    const headers = document.querySelectorAll('th');
                    headers.forEach(header => {
                        if (header.textContent.includes('الأعمال المرحلة')) {
                            header.innerHTML = `الأعمال المرحلة (${data.subject.total_mark * 0.4})`;
                        } else if (header.textContent.includes('امتحان الدور الثاني')) {
                            header.innerHTML = `امتحان الدور الثاني (${data.subject.total_mark * 0.6})`;
                        }
                    });

                    // ملء جدول الدرجات
                    gradesTableBody.innerHTML = '';

                    data.students.forEach((student, index) => {
                        const row = document.createElement('tr');
                        row.className = 'hover:bg-gray-50';
                        row.dataset.studentId = student.id;

                        row.innerHTML = `
                            <td class="py-3 px-4 border-b text-center">${index + 1}</td>
                            <td class="py-3 px-4 border-b text-right">${student.name}</td>
                            <td class="py-3 px-4 border-b text-center">${student.secret_number || '-'}</td>
                            <td class="py-3 px-4 border-b text-center">${student.seat_number || '-'}</td>
                            <td class="py-3 px-4 border-b text-center bg-yellow-50 font-semibold">${student.combined_classwork}</td>
                            <td class="py-3 px-4 border-b text-center bg-blue-50">
                                <input type="number" class="exam-mark w-full px-2 py-1 border rounded text-center"
                                       min="0" max="${data.subject.total_mark * 0.6}" step="0.5"
                                       value="${student.second_chance_exam || ''}"
                                       placeholder="0.0">
                            </td>
                            <td class="py-3 px-4 border-b text-center bg-green-50 total-mark font-semibold">
                                ${student.total_mark ? (Number.isInteger(student.total_mark) ? student.total_mark : student.total_mark.toFixed(2)) : '-'}
                            </td>
                            <td class="py-3 px-4 border-b text-center bg-red-50 result">
                                ${student.is_passed === true ? '<span class="text-green-600 font-medium">ناجح</span>' :
                                  student.is_passed === false ? '<span class="text-red-600 font-medium">راسب</span>' : '-'}
                            </td>
                        `;

                        gradesTableBody.appendChild(row);
                    });

                    // إظهار جدول الدرجات
                    gradesContainer.classList.remove('hidden');

                    // إضافة مستمعي الأحداث لحقول الإدخال
                    setupInputListeners();
                } else {
                    // إظهار رسالة عدم وجود طلاب متعثرين
                    noStudentsMessage.classList.remove('hidden');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                loadingIndicator.classList.add('hidden');
                alert('حدث خطأ أثناء تحميل البيانات');
            });
    });

    // إعداد مستمعي الأحداث لحقول الإدخال
    function setupInputListeners() {
        document.querySelectorAll('.exam-mark').forEach(input => {
            input.addEventListener('input', function() {
                // التحقق من صحة القيمة
                if (this.value < 0) this.value = 0;

                // الحصول على الصف
                const row = this.closest('tr');

                // الحصول على قيم الدرجات
                const classworkInput = row.querySelector('.classwork-mark');
                const examInput = row.querySelector('.exam-mark');
                const totalCell = row.querySelector('.total-mark');
                const resultCell = row.querySelector('.result');

                // الحصول على الدرجة الكلية للمادة من البيانات المستلمة من الخادم
                const subjectTotalMark = window.subjectTotalMark || 40;

                // حساب الدرجة الكلية للفصل (نصف الدرجة الكلية للمادة)
                const semesterTotalMark = subjectTotalMark / 2;

                // الحدود القصوى للدرجات
                const maxExam = semesterTotalMark * 0.6; // مثلاً: 24 درجة إذا كانت الدرجة الكلية للفصل 40

                // التحقق من درجة الامتحان إذا تم إدخالها
                if (this.value) {
                    const examMark = parseFloat(this.value);
                    if (examMark > maxExam) {
                        alert(`تنبيه: درجة الامتحان لا يمكن أن تتجاوز ${maxExam.toFixed(1)} (60% من الدرجة الكلية للفصل ${semesterTotalMark.toFixed(1)})`);
                        this.value = maxExam;
                    }
                }

                // التحقق من وجود قيم في كلا الحقلين لحساب المجموع
                if (classworkInput.value && examInput.value) {
                    // تحويل القيم إلى أرقام
                    const classworkMark = parseFloat(classworkInput.value);
                    const examMark = parseFloat(examInput.value);

                    // الحصول على الدرجة الكلية للمادة من البيانات المستلمة من الخادم
                    const subjectTotalMark = window.subjectTotalMark || 40;

                    try {
                        // حساب الدرجة الكلية للفصل (نصف الدرجة الكلية للمادة)
                        const semesterTotalMark = subjectTotalMark / 2;

                        // التأكد من أن درجة الأعمال لا تتجاوز 40% من الدرجة الكلية للفصل
                        const maxClasswork = semesterTotalMark * 0.4; // مثلاً: 16 درجة إذا كانت الدرجة الكلية للفصل 40

                        // إذا تجاوزت درجة الأعمال الحد الأقصى، نعرض تنبيهًا
                        if (classworkMark > maxClasswork) {
                            alert(`تنبيه: درجة أعمال الفصل لا يمكن أن تتجاوز ${maxClasswork.toFixed(1)} (40% من الدرجة الكلية للفصل ${semesterTotalMark.toFixed(1)})`);
                            classworkInput.value = maxClasswork;
                            return; // نتوقف عن المتابعة حتى يصحح المستخدم الدرجة
                        }

                        // التأكد من أن درجة الامتحان لا تتجاوز 60% من الدرجة الكلية للفصل
                        const maxExam = semesterTotalMark * 0.6; // مثلاً: 24 درجة إذا كانت الدرجة الكلية للفصل 40

                        // إذا تجاوزت درجة الامتحان الحد الأقصى، نعرض تنبيهًا
                        if (examMark > maxExam) {
                            alert(`تنبيه: درجة الامتحان لا يمكن أن تتجاوز ${maxExam.toFixed(1)} (60% من الدرجة الكلية للفصل ${semesterTotalMark.toFixed(1)})`);
                            examInput.value = maxExam;
                            return; // نتوقف عن المتابعة حتى يصحح المستخدم الدرجة
                        }

                        const validClassworkMark = classworkMark;
                        const validExamMark = examMark;

                        // التحقق من شرط الامتحان (40% من درجة الامتحان)
                        const minExamMark = maxExam * 0.4; // 9.6 درجة إذا كانت درجة الامتحان 24
                        const minExamRequirement = validExamMark >= minExamMark;

                        // حساب المجموع الكلي
                        const totalPoints = validClassworkMark + validExamMark;

                        // عرض المجموع (النقاط) فقط إذا تحقق شرط الامتحان
                        if (minExamRequirement) {
                            // تنسيق الرقم: إذا كان رقمًا صحيحًا، نعرضه بدون كسور عشرية
                            // وإذا كان يحتوي على كسور، نعرضه بكسرين عشريين
                            totalCell.textContent = Number.isInteger(totalPoints) ? totalPoints.toString() : totalPoints.toFixed(2);
                        } else {
                            totalCell.textContent = "-";
                        }

                        // تخزين القيم في خصائص مخصصة للصف
                        row.dataset.classworkMark = validClassworkMark;
                        row.dataset.examMark = validExamMark;
                        row.dataset.totalPoints = totalPoints;
                        row.dataset.minExamRequirement = minExamRequirement;
                    } catch (error) {
                        console.error("خطأ في حساب الدرجات:", error);
                        totalCell.textContent = "خطأ";
                    }

                    // ويجب أن يكون المجموع الكلي 50% على الأقل
                    const minTotalMark = semesterTotalMark * 0.5; // مثلاً: 20 درجة إذا كانت الدرجة الكلية للفصل 40
                    const minTotalRequirement = totalPoints >= minTotalMark;

                    // طباعة قيم التشخيص
                    console.log(`درجة الأعمال: ${classworkMark}, درجة الامتحان: ${examMark}, الدرجة الكلية للمادة: ${subjectTotalMark}`);
                    console.log(`الحد الأقصى للأعمال: ${maxClasswork}, الحد الأقصى للامتحان: ${maxExam}`);
                    console.log(`درجة الأعمال المعدلة: ${validClassworkMark}, درجة الامتحان المعدلة: ${validExamMark}`);
                    console.log(`المجموع: ${totalPoints}`);
                    console.log(`الحد الأدنى للامتحان: ${minExamMark}, الحد الأدنى للمجموع: ${minTotalMark}`);
                    console.log(`شرط الامتحان: ${row.dataset.minExamRequirement}, شرط المجموع: ${minTotalRequirement}`);

                    // الحصول على قيمة شرط الامتحان من خصائص الصف
                    const minExamRequirement = row.dataset.minExamRequirement === "true";
                    const isPassed = minExamRequirement && minTotalRequirement;

                    resultCell.innerHTML = isPassed ?
                        '<span class="text-green-600 font-medium">ناجح</span>' :
                        '<span class="text-red-600 font-medium">راسب</span>';
                } else {
                    // إعادة تعيين الخلايا
                    totalCell.textContent = '-';
                    resultCell.textContent = '-';
                }
            });
        });
    }

    // حفظ درجات الدور الثاني
    saveGradesBtn.addEventListener('click', function() {
        const subjectId = subjectSelect.value;
        const subjectTotalMark = window.subjectTotalMark || 40;

        // التحقق من صحة البيانات قبل الإرسال
        let hasErrors = false;
        document.querySelectorAll('#grades-table-body tr').forEach(row => {
            const examInput = row.querySelector('.exam-mark');

            // الحد الأقصى لامتحان الدور الثاني (60% من الدرجة الكلية للمادة)
            // هذا يعادل مجموع امتحاني الفصلين
            const maxExam = subjectTotalMark * 0.6;

            if (examInput.value && parseFloat(examInput.value) > maxExam) {
                examInput.classList.add('border-red-500');
                hasErrors = true;
                alert(`تنبيه: درجة امتحان الدور الثاني لا يمكن أن تتجاوز ${maxExam.toFixed(1)}\n(يعادل مجموع امتحاني الفصلين: ${(maxExam/2).toFixed(1)} + ${(maxExam/2).toFixed(1)})`);
            } else {
                examInput.classList.remove('border-red-500');
            }
        });

        if (hasErrors) {
            const maxExam = subjectTotalMark * 0.6;
            alert(`يوجد درجات تتجاوز الحد الأقصى المسموح به. يرجى تصحيح الدرجات قبل الحفظ.\n\nالحد الأقصى لامتحان الدور الثاني: ${maxExam.toFixed(1)} (مجموع امتحاني الفصلين)`);
            return;
        }

        // جمع بيانات الدرجات
        const grades = [];

        document.querySelectorAll('#grades-table-body tr').forEach(row => {
            const studentId = row.dataset.studentId;
            const examMark = row.querySelector('.exam-mark').value;

            if (examMark) {
                grades.push({
                    student_id: studentId,
                    exam_mark: examMark
                });
            }
        });

        // إرسال البيانات إلى الخادم
        fetch('/second-chance/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                subject_id: subjectId,
                grades: grades
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حفظ درجات الدور الثاني بنجاح');

                // إفراغ حقول الإدخال بعد الحفظ
                document.querySelectorAll('#grades-table-body tr').forEach(row => {
                    const examInput = row.querySelector('.exam-mark');
                    const totalCell = row.querySelector('.total-mark');
                    const resultCell = row.querySelector('.result');

                    if (examInput) examInput.value = '';
                    if (totalCell) totalCell.textContent = '-';
                    if (resultCell) resultCell.textContent = '-';
                });
            } else {
                alert('حدث خطأ أثناء حفظ الدرجات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حفظ الدرجات');
        });
    });
</script>
{% endblock %}
