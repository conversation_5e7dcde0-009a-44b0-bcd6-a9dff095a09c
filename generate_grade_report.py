from reportlab.lib.pagesizes import A4, landscape
from reportlab.pdfgen import canvas
from reportlab.lib import colors
from reportlab.platypus import Table, TableStyle
from reportlab.lib.units import mm
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
import arabic_reshaper
from bidi.algorithm import get_display

# إعداد الخط العربي (تأكد من وجود ملف الخط في نفس المجلد)
pdfmetrics.registerFont(TTFont('Amiri', 'Amiri-Regular.ttf'))

def reshape_arabic(text):
    return get_display(arabic_reshaper.reshape(text))

def draw_header(c):
    c.setFont('<PERSON><PERSON>', 12)
    c.drawString(30, 560, reshape_arabic('كشف اعتماد درجات أعمال السنة وامتحان نهاية الفصل للصف الأول الثانوي - العام الدراسي 2022-2023م'))
    c.setFont('<PERSON><PERSON>', 10)
    c.drawString(30, 545, reshape_arabic('المدرسة: شهداء الكر التعليم الثانوي'))
    c.drawString(400, 545, reshape_arabic('المنطقة: الجفرة'))
    c.drawString(30, 530, reshape_arabic('الفصل: الأول'))
    c.drawString(400, 530, reshape_arabic('الصف: الأول الثانوي'))
    c.line(20, 525, 800, 525)

def draw_footer(c):
    c.setFont('Amiri', 10)
    c.drawString(30, 60, reshape_arabic('مدير المدرسة'))
    c.drawString(200, 60, reshape_arabic('ختم المدرسة'))
    c.drawString(400, 60, reshape_arabic('ختم الامتحانات'))
    c.drawString(600, 60, reshape_arabic('ختم مكتب الامتحانات'))

def generate_table_data():
    # بيانات الأعمدة (المواد)
    subjects = [
        'اللغة العربية', 'الرياضيات', 'الفيزياء', 'الكيمياء', 'الأحياء',
        'اللغة الإنجليزية', 'الحاسوب', 'الجغرافيا', 'التاريخ', 'التربية الإسلامية'
    ]
    # بيانات الطلاب (مثال)
    students = [
        {'name': 'محمد علي أحمد', 'seat': '4373926', 'grades': [80, 75, 70, 85, 90, 60, 77, 88, 92, 81]},
        {'name': 'سارة محمد سالم', 'seat': '4373900', 'grades': [78, 80, 65, 70, 85, 75, 80, 90, 88, 79]},
        {'name': 'حسن أحمد عوض', 'seat': '4373908', 'grades': [60, 70, 80, 90, 85, 80, 70, 60, 75, 80]},
    ]
    data = [[reshape_arabic('الاسم'), reshape_arabic('رقم الجلوس')] + [reshape_arabic(s) for s in subjects] + [reshape_arabic('المجموع')]]
    for s in students:
        total = sum(s['grades'])
        row = [reshape_arabic(s['name']), s['seat']] + s['grades'] + [total]
        data.append(row)
    return data

def create_grade_report(filename='grade_report.pdf'):
    c = canvas.Canvas(filename, pagesize=landscape(A4))
    draw_header(c)
    data = generate_table_data()
    table = Table(data, colWidths=[70, 60] + [45]*10 + [50])
    style = TableStyle([
        ('FONTNAME', (0,0), (-1,-1), 'Amiri'),
        ('FONTSIZE', (0,0), (-1,-1), 10),
        ('ALIGN', (0,0), (-1,-1), 'CENTER'),
        ('GRID', (0,0), (-1,-1), 0.5, colors.black),
        ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
    ])
    table.setStyle(style)
    table.wrapOn(c, 50, 400)
    table.drawOn(c, 30, 350)
    draw_footer(c)
    c.save()

if __name__ == '__main__':
    create_grade_report()
    print('تم إنشاء كشف الدرجات بنجاح: grade_report.pdf')
