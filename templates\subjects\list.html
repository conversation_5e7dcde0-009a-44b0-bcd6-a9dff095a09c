{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">إدارة المواد الدراسية</h1>
        <a href="{{ url_for('add_subject') }}" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-plus ml-1"></i> إضافة مادة جديدة
        </a>
    </div>
    
    <!-- اختيار الصف الدراسي -->
    <div class="mb-6 bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-semibold mb-3">اختر الصف الدراسي</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            {% for grade in grades %}
            <a href="{{ url_for('list_subjects', grade_id=grade.id) }}" class="block p-3 rounded-lg border {% if selected_grade and selected_grade.id == grade.id %}bg-blue-100 border-blue-500{% else %}bg-white hover:bg-gray-100 border-gray-200{% endif %} transition-colors">
                <div class="font-medium">{{ grade.name }}</div>
                <div class="text-sm text-gray-600">{{ grade.track }}</div>
            </a>
            {% else %}
            <div class="col-span-3 text-center py-4 text-gray-500">
                لا توجد صفوف دراسية مسجلة. يرجى إضافة صفوف دراسية أولاً.
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- قائمة المواد -->
    {% if selected_grade %}
    <div>
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">مواد {{ selected_grade.name }} - {{ selected_grade.track }}</h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="py-3 px-4 border-b text-right">#</th>
                        <th class="py-3 px-4 border-b text-right">اسم المادة</th>
                        <th class="py-3 px-4 border-b text-right">الدرجة الكلية</th>
                        <th class="py-3 px-4 border-b text-right">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for subject in subjects %}
                    <tr class="hover:bg-gray-50">
                        <td class="py-3 px-4 border-b">{{ loop.index }}</td>
                        <td class="py-3 px-4 border-b">{{ subject.name }}</td>
                        <td class="py-3 px-4 border-b">{{ subject.total_mark }}</td>
                        <td class="py-3 px-4 border-b">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="{{ url_for('edit_subject', id=subject.id) }}" class="text-blue-500 hover:text-blue-700">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="text-red-500 hover:text-red-700 delete-subject" data-id="{{ subject.id }}" data-name="{{ subject.name }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="4" class="py-6 text-center text-gray-500">لا توجد مواد مسجلة لهذا الصف الدراسي</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="text-center py-10 text-gray-500">
        <i class="fas fa-book text-5xl mb-4 text-gray-300"></i>
        <p>يرجى اختيار صف دراسي لعرض المواد</p>
    </div>
    {% endif %}
</div>

<!-- نموذج حذف المادة -->
<div id="delete-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h3 class="text-xl font-bold mb-4">تأكيد الحذف</h3>
        <p class="mb-6">هل أنت متأكد من حذف المادة <span id="subject-name" class="font-semibold"></span>؟</p>
        <div class="flex justify-end space-x-4 space-x-reverse">
            <button id="cancel-delete" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded transition-colors">
                إلغاء
            </button>
            <form id="delete-form" method="POST">
                <button type="submit" class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded transition-colors">
                    حذف
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // حذف المادة
    const deleteModal = document.getElementById('delete-modal');
    const subjectNameSpan = document.getElementById('subject-name');
    const deleteForm = document.getElementById('delete-form');
    const cancelDelete = document.getElementById('cancel-delete');
    
    document.querySelectorAll('.delete-subject').forEach(button => {
        button.addEventListener('click', function() {
            const subjectId = this.dataset.id;
            const subjectName = this.dataset.name;
            
            subjectNameSpan.textContent = subjectName;
            deleteForm.action = `/subjects/delete/${subjectId}`;
            deleteModal.classList.remove('hidden');
        });
    });
    
    cancelDelete.addEventListener('click', function() {
        deleteModal.classList.add('hidden');
    });
</script>
{% endblock %}
