{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">تعديل الصف الدراسي</h1>
        <a href="{{ url_for('list_grades') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-arrow-right ml-1"></i> العودة
        </a>
    </div>
    
    <form method="POST" action="{{ url_for('edit_grade', id=grade.id) }}">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
                <label for="name" class="block text-gray-700 font-medium mb-2">اسم الصف الدراسي</label>
                <input type="text" id="name" name="name" value="{{ grade.name }}" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                <p class="text-gray-500 text-sm mt-1">مثال: الصف الأول الثانوي</p>
            </div>
            
            <div>
                <label for="level" class="block text-gray-700 font-medium mb-2">المستوى</label>
                <input type="number" id="level" name="level" value="{{ grade.level }}" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                <p class="text-gray-500 text-sm mt-1">مثال: 10 للصف الأول الثانوي، 11 للصف الثاني الثانوي، إلخ.</p>
            </div>
            
            <div>
                <label for="track" class="block text-gray-700 font-medium mb-2">المسار</label>
                <select id="track" name="track" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                    <option value="عام" {% if grade.track == 'عام' %}selected{% endif %}>عام</option>
                    <option value="علمي" {% if grade.track == 'علمي' %}selected{% endif %}>علمي</option>
                    <option value="أدبي" {% if grade.track == 'أدبي' %}selected{% endif %}>أدبي</option>
                </select>
            </div>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors">
                <i class="fas fa-save ml-1"></i> حفظ التغييرات
            </button>
        </div>
    </form>
</div>
{% endblock %}
