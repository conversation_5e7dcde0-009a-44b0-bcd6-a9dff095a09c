// العناصر
const classSelect = document.getElementById('class-select');
const subjectSelect = document.getElementById('subject-select');
const periodSelect = document.getElementById('period-select');
const loadStudentsBtn = document.getElementById('load-students-btn');
const gradesContainer = document.getElementById('grades-container');
const noStudentsMessage = document.getElementById('no-students-message');
const loadingIndicator = document.getElementById('loading-indicator');
const gradesTableBody = document.getElementById('grades-table-body');
const subjectNameSpan = document.getElementById('subject-name');
const totalMarkSpan = document.getElementById('total-mark');
const saveGradesBtn = document.getElementById('save-grades-btn');

// خيارات الرصد
const semesterTypeRadios = document.querySelectorAll('input[name="semester-type"]');
const gradeTypeRadios = document.querySelectorAll('input[name="grade-type"]');
const recordMethodRadios = document.querySelectorAll('input[name="record-method"]');

// الحصول على القيم المحددة
function getSelectedSemesterType() {
    return document.querySelector('input[name="semester-type"]:checked').value;
}

function getSelectedGradeType() {
    return document.querySelector('input[name="grade-type"]:checked').value;
}

function getSelectedRecordMethod() {
    return document.querySelector('input[name="record-method"]:checked').value;
}

// تفعيل/تعطيل الحقول
classSelect.addEventListener('change', function() {
    const selectedClass = this.options[this.selectedIndex];
    console.log('تم تغيير الفصل:', selectedClass ? selectedClass.textContent : 'غير محدد');

    if (selectedClass && selectedClass.value) {
        // تحديث قائمة المواد بناءً على الفصل المحدد
        const classId = selectedClass.value;
        console.log('معرف الفصل:', classId);
        
        // إظهار مؤشر التحميل
        loadingIndicator.classList.remove('hidden');
        
        // تعطيل حقل المادة أثناء التحميل
        subjectSelect.disabled = true;
        
        // طلب المواد من الخادم
        fetch(`/api/class/${classId}/subjects`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('حدث خطأ أثناء تحميل المواد');
                }
                return response.json();
            })
            .then(data => {
                // إخفاء مؤشر التحميل
                loadingIndicator.classList.add('hidden');
                
                // تفريغ قائمة المواد
                while (subjectSelect.options.length > 1) {
                    subjectSelect.remove(1);
                }
                
                // إضافة المواد الجديدة
                if (data.subjects && data.subjects.length > 0) {
                    data.subjects.forEach(subject => {
                        const option = document.createElement('option');
                        option.value = subject.id;
                        option.textContent = subject.name;
                        option.dataset.gradeId = subject.grade_id;
                        subjectSelect.appendChild(option);
                    });
                    
                    // تفعيل حقل المادة
                    subjectSelect.disabled = false;
                    console.log(`تم تحميل ${data.subjects.length} مادة`);
                } else {
                    console.log('لا توجد مواد متاحة لهذا الفصل');
                    alert('لا توجد مواد متاحة لهذا الفصل');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                loadingIndicator.classList.add('hidden');
                alert('حدث خطأ أثناء تحميل المواد. يرجى المحاولة مرة أخرى.');
            });
    } else {
        subjectSelect.disabled = true;
        periodSelect.disabled = true;
        loadStudentsBtn.disabled = true;
    }

    // إعادة تعيين الاختيارات
    subjectSelect.selectedIndex = 0;
    periodSelect.selectedIndex = 0;
    periodSelect.disabled = true;
    loadStudentsBtn.disabled = true;
});

subjectSelect.addEventListener('change', function() {
    if (this.value) {
        periodSelect.disabled = false;
    } else {
        periodSelect.disabled = true;
        loadStudentsBtn.disabled = true;
    }

    // إعادة تعيين الاختيارات
    periodSelect.selectedIndex = 0;
    loadStudentsBtn.disabled = true;
});

periodSelect.addEventListener('change', function() {
    loadStudentsBtn.disabled = !this.value;
});

// تحديث عرض الجدول بناءً على الخيارات المحددة
function updateTableDisplay() {
    const gradeType = getSelectedGradeType();
    const recordMethod = getSelectedRecordMethod();

    // إخفاء/إظهار أعمدة الجدول حسب طريقة الرصد
    const studentNameHeader = document.querySelector('.student-name-header');
    const studentIdHeader = document.querySelector('.student-id-header');
    const studentSeatHeader = document.querySelector('.student-seat-header');

    // إخفاء جميع الأعمدة أولاً
    studentNameHeader.style.display = 'none';
    studentIdHeader.style.display = 'none';
    studentSeatHeader.style.display = 'none';

    // إظهار العمود المناسب حسب طريقة الرصد
    if (recordMethod === 'name') {
        studentNameHeader.style.display = '';
    } else if (recordMethod === 'secret-number') {
        studentIdHeader.style.display = '';
    } else if (recordMethod === 'seat-number') {
        studentSeatHeader.style.display = '';
    }

    // إخفاء/إظهار أعمدة الدرجات حسب نوع الدرجات
    const classworkHeader = document.querySelector('.classwork-header');
    const examHeader = document.querySelector('.exam-header');

    if (gradeType === 'both') {
        classworkHeader.style.display = '';
        examHeader.style.display = '';
    } else if (gradeType === 'classwork') {
        classworkHeader.style.display = '';
        examHeader.style.display = 'none';
    } else if (gradeType === 'exam') {
        classworkHeader.style.display = 'none';
        examHeader.style.display = '';
    }
}

// إضافة مستمعي الأحداث للخيارات
gradeTypeRadios.forEach(radio => {
    radio.addEventListener('change', updateTableDisplay);
});

recordMethodRadios.forEach(radio => {
    radio.addEventListener('change', updateTableDisplay);
});

// تحميل الطلاب
loadStudentsBtn.addEventListener('click', function() {
    const classId = classSelect.value;
    const subjectId = subjectSelect.value;
    const periodId = periodSelect.value;
    const semesterType = getSelectedSemesterType();
    const gradeType = getSelectedGradeType();
    const recordMethod = getSelectedRecordMethod();

    if (!classId || !subjectId || !periodId) {
        alert('يرجى اختيار الفصل والمادة والفترة');
        return;
    }

    // إظهار مؤشر التحميل
    gradesContainer.classList.add('hidden');
    noStudentsMessage.classList.add('hidden');
    loadingIndicator.classList.remove('hidden');

    // طلب البيانات من الخادم
    fetch(`/grades/students?class_id=${classId}&subject_id=${subjectId}&period_id=${periodId}&semester_type=${semesterType}`)
        .then(response => {
            // إخفاء مؤشر التحميل
            loadingIndicator.classList.add('hidden');
            
            if (!response.ok) {
                if (response.status === 403) {
                    // خطأ متعلق بحالة اعتماد الفترة
                    return response.json().then(errorData => {
                        throw new Error(errorData.error);
                    });
                }
                throw new Error('حدث خطأ أثناء تحميل البيانات');
            }
            
            return response.json();
        })
        .then(data => {
            if (data.students && data.students.length > 0) {
                // عرض اسم المادة والدرجة الكلية
                subjectNameSpan.textContent = data.subject.name;
                totalMarkSpan.textContent = data.subject.total_mark;

                // ملء جدول الدرجات
                gradesTableBody.innerHTML = '';

                data.students.forEach((student, index) => {
                    const row = document.createElement('tr');
                    row.className = 'hover:bg-gray-50';
                    row.dataset.studentId = student.id;

                    // بناء صف الجدول
                    row.innerHTML = `
                        <td class="py-3 px-4 border-b text-center">${index + 1}</td>
                        <td class="py-3 px-4 border-b text-right student-id-cell" style="display: ${recordMethod === 'secret-number' ? '' : 'none'}">${student.secret_number || 'غير متوفر'}</td>
                        <td class="py-3 px-4 border-b text-right student-seat-cell" style="display: ${recordMethod === 'seat-number' ? '' : 'none'}">${student.seat_number || 'غير متوفر'}</td>
                        <td class="py-3 px-4 border-b text-right student-name-cell" style="display: ${recordMethod === 'name' ? '' : 'none'}">${student.name}</td>
                        ${gradeType === 'both' || gradeType === 'classwork' ?
                            `<td class="py-3 px-4 border-b text-center classwork-cell">
                                <input type="number" class="classwork-mark w-20 border border-gray-300 rounded py-1 px-2 text-center" min="0" max="${data.subject.total_mark}" value="${student.classwork_mark || ''}">
                            </td>` : ''}
                        ${gradeType === 'both' || gradeType === 'exam' ?
                            `<td class="py-3 px-4 border-b text-center exam-cell">
                                <input type="number" class="exam-mark w-20 border border-gray-300 rounded py-1 px-2 text-center" min="0" max="${data.subject.total_mark}" value="${student.exam_mark || ''}">
                            </td>` : ''}
                        <td class="py-3 px-4 border-b text-center total-mark ${student.is_passed === true ? 'text-green-600' : student.is_passed === false ? 'text-red-600' : ''}">
                            ${student.total_mark ? (Number.isInteger(student.total_mark) ? student.total_mark : student.total_mark.toFixed(2)) : '-'}
                        </td>
                    `;

                    gradesTableBody.appendChild(row);
                });

                // إظهار جدول الدرجات
                gradesContainer.classList.remove('hidden');

                // تحديث عرض الجدول
                updateTableDisplay();

                // إضافة مستمعي الأحداث لحقول الإدخال
                setupInputListeners();
            } else {
                // إظهار رسالة عدم وجود طلاب
                noStudentsMessage.classList.remove('hidden');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            loadingIndicator.classList.add('hidden');
            alert(error.message || 'حدث خطأ أثناء تحميل البيانات');
        });
});

// إعداد مستمعي الأحداث لحقول الإدخال
function setupInputListeners() {
    document.querySelectorAll('.classwork-mark, .exam-mark').forEach(input => {
        input.addEventListener('input', function() {
            // التحقق من صحة القيمة
            if (this.value < 0) this.value = 0;

            // الحصول على الصف
            const row = this.closest('tr');

            // الحصول على قيم الدرجات
            const classworkInput = row.querySelector('.classwork-mark');
            const examInput = row.querySelector('.exam-mark');
            const totalCell = row.querySelector('.total-mark');

            // الحصول على الدرجة الكلية للمادة
            const subjectTotalMark = parseFloat(totalMarkSpan.textContent);

            // حساب الدرجة الكلية للفصل (نصف الدرجة الكلية للمادة)
            const semesterTotalMark = subjectTotalMark / 2;

            // الحدود القصوى للدرجات
            const maxClasswork = semesterTotalMark * 0.4; // مثلاً: 16 درجة إذا كانت الدرجة الكلية للفصل 40
            const maxExam = semesterTotalMark * 0.6; // مثلاً: 24 درجة إذا كانت الدرجة الكلية للفصل 40

            // التحقق من نوع الحقل وتطبيق الحد الأقصى المناسب
            if (this.classList.contains('classwork-mark') && this.value) {
                const classworkMark = parseFloat(this.value);
                if (classworkMark > maxClasswork) {
                    alert(`تنبيه: درجة أعمال الفصل لا يمكن أن تتجاوز ${maxClasswork.toFixed(1)} (40% من الدرجة الكلية للفصل ${semesterTotalMark.toFixed(1)})`);
                    this.value = maxClasswork;
                }
            }

            if (this.classList.contains('exam-mark') && this.value) {
                const examMark = parseFloat(this.value);
                if (examMark > maxExam) {
                    alert(`تنبيه: درجة الامتحان لا يمكن أن تتجاوز ${maxExam.toFixed(1)} (60% من الدرجة الكلية للفصل ${semesterTotalMark.toFixed(1)})`);
                    this.value = maxExam;
                }
            }

            // التحقق من وجود قيم في كلا الحقلين لحساب المجموع
            if (classworkInput && examInput && classworkInput.value && examInput.value) {
                // تحويل القيم إلى أرقام
                const classworkMark = parseFloat(classworkInput.value);
                const examMark = parseFloat(examInput.value);

                // التحقق من شرط الامتحان (40% من درجة الامتحان)
                const minExamMark = maxExam * 0.4; // 9.6 درجة إذا كانت درجة الامتحان 24
                const minExamRequirement = examMark >= minExamMark;

                // حساب المجموع الكلي
                const totalPoints = classworkMark + examMark;

                // التحقق من شرط المجموع (50% من الدرجة الكلية للفصل)
                const minTotalMark = semesterTotalMark * 0.5; // مثلاً: 20 درجة إذا كانت الدرجة الكلية للفصل 40
                const minTotalRequirement = totalPoints >= minTotalMark;

                // تحديد النجاح أو الرسوب
                const isPassed = minExamRequirement && minTotalRequirement;

                // تحديث خلية المجموع
                if (minExamRequirement) {
                    totalCell.textContent = Number.isInteger(totalPoints) ? totalPoints : totalPoints.toFixed(2);
                    totalCell.className = `py-3 px-4 border-b text-center total-mark ${isPassed ? 'text-green-600' : 'text-red-600'}`;
                } else {
                    totalCell.textContent = '-';
                    totalCell.className = 'py-3 px-4 border-b text-center total-mark';
                }
            }
        });
    });
}

// حفظ الدرجات
saveGradesBtn.addEventListener('click', function() {
    const classId = classSelect.value;
    const subjectId = subjectSelect.value;
    const periodId = periodSelect.value;
    const semesterType = getSelectedSemesterType();
    const gradeType = getSelectedGradeType();

    if (!classId || !subjectId || !periodId) {
        alert('يرجى اختيار الفصل والمادة والفترة');
        return;
    }

    // جمع الدرجات
    const grades = [];
    document.querySelectorAll('#grades-table-body tr').forEach(row => {
        const studentId = row.dataset.studentId;
        const classworkInput = row.querySelector('.classwork-mark');
        const examInput = row.querySelector('.exam-mark');

        const gradeData = {
            student_id: studentId
        };

        if (gradeType === 'both' || gradeType === 'classwork') {
            gradeData.classwork_mark = classworkInput ? classworkInput.value : null;
        }

        if (gradeType === 'both' || gradeType === 'exam') {
            gradeData.exam_mark = examInput ? examInput.value : null;
        }

        grades.push(gradeData);
    });

    // إظهار مؤشر التحميل
    loadingIndicator.classList.remove('hidden');

    // إرسال البيانات إلى الخادم
    fetch('/grades/save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            subject_id: subjectId,
            period_id: periodId,
            semester_type: semesterType,
            grade_type: gradeType,
            grades: grades
        })
    })
    .then(response => response.json())
    .then(data => {
        // إخفاء مؤشر التحميل
        loadingIndicator.classList.add('hidden');

        if (data.success) {
            alert('تم حفظ الدرجات بنجاح');
            
            // إعادة تحميل الطلاب لعرض الدرجات المحدثة
            loadStudentsBtn.click();
        } else {
            // عرض رسالة الخطأ
            alert(data.message || 'حدث خطأ أثناء حفظ الدرجات');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        loadingIndicator.classList.add('hidden');
        alert('حدث خطأ أثناء حفظ الدرجات');
    });
});