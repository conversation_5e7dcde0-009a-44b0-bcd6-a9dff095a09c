{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">إدارة الفصول</h1>
        <a href="{{ url_for('add_class') }}" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-plus ml-1"></i> إضافة فصل جديد
        </a>
    </div>
    
    <!-- اختيار الصف الدراسي -->
    <div class="mb-6 bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-semibold mb-3">اختر الصف الدراسي</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            {% for grade in grades %}
            <a href="{{ url_for('list_classes', grade_id=grade.id) }}" class="block p-3 rounded-lg border {% if selected_grade and selected_grade.id == grade.id %}bg-blue-100 border-blue-500{% else %}bg-white hover:bg-gray-100 border-gray-200{% endif %} transition-colors">
                <div class="font-medium">{{ grade.name }}</div>
                <div class="text-sm text-gray-600">{{ grade.track }}</div>
            </a>
            {% else %}
            <div class="col-span-3 text-center py-4 text-gray-500">
                لا توجد صفوف دراسية مسجلة. يرجى إضافة صفوف دراسية أولاً.
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- قائمة الفصول -->
    {% if selected_grade %}
    <div>
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">فصول {{ selected_grade.name }} - {{ selected_grade.track }}</h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="py-3 px-4 border-b text-right">#</th>
                        <th class="py-3 px-4 border-b text-right">اسم الفصل</th>
                        <th class="py-3 px-4 border-b text-right">العام الدراسي</th>
                        <th class="py-3 px-4 border-b text-right">عدد الطلاب</th>
                        <th class="py-3 px-4 border-b text-right">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for class in classes %}
                    <tr class="hover:bg-gray-50">
                        <td class="py-3 px-4 border-b">{{ loop.index }}</td>
                        <td class="py-3 px-4 border-b">{{ class.name }}</td>
                        <td class="py-3 px-4 border-b">{{ class.academic_year }}</td>
                        <td class="py-3 px-4 border-b">{{ class.students|length }}</td>
                        <td class="py-3 px-4 border-b">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="{{ url_for('edit_class', id=class.id) }}" class="text-blue-500 hover:text-blue-700">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('list_students', class_id=class.id) }}" class="text-green-500 hover:text-green-700">
                                    <i class="fas fa-users"></i>
                                </a>
                                <button class="text-red-500 hover:text-red-700 delete-class" data-id="{{ class.id }}" data-name="{{ class.name }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="5" class="py-6 text-center text-gray-500">لا توجد فصول مسجلة لهذا الصف الدراسي</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="text-center py-10 text-gray-500">
        <i class="fas fa-chalkboard text-5xl mb-4 text-gray-300"></i>
        <p>يرجى اختيار صف دراسي لعرض الفصول</p>
    </div>
    {% endif %}
</div>

<!-- نموذج حذف الفصل -->
<div id="delete-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h3 class="text-xl font-bold mb-4">تأكيد الحذف</h3>
        <p class="mb-6">هل أنت متأكد من حذف الفصل <span id="class-name" class="font-semibold"></span>؟</p>
        <div class="flex justify-end space-x-4 space-x-reverse">
            <button id="cancel-delete" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded transition-colors">
                إلغاء
            </button>
            <form id="delete-form" method="POST">
                <button type="submit" class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded transition-colors">
                    حذف
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // حذف الفصل
    const deleteModal = document.getElementById('delete-modal');
    const classNameSpan = document.getElementById('class-name');
    const deleteForm = document.getElementById('delete-form');
    const cancelDelete = document.getElementById('cancel-delete');
    
    document.querySelectorAll('.delete-class').forEach(button => {
        button.addEventListener('click', function() {
            const classId = this.dataset.id;
            const className = this.dataset.name;
            
            classNameSpan.textContent = className;
            deleteForm.action = `/classes/delete/${classId}`;
            deleteModal.classList.remove('hidden');
        });
    });
    
    cancelDelete.addEventListener('click', function() {
        deleteModal.classList.add('hidden');
    });
</script>
{% endblock %}
