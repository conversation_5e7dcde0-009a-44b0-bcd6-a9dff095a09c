from flask import Flask
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from app_config import configure_app
from app_routes import register_routes
from models import db, init_db

# إنشاء تطبيق Flask
app = Flask(__name__)

# تكوين التطبيق
configure_app(app)

# تسجيل المسارات
register_routes(app)

# تهيئة قاعدة البيانات
init_db(app)

if __name__ == '__main__':
    # تشغيل Flask مباشرة على المنفذ 5000
    app.run(debug=True, host='127.0.0.1', port=5000)