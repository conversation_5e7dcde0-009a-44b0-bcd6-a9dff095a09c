import os
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from database.models import Student, Grade, Subject, AcademicPeriod, Class
from utils.calculations import calculate_subject_grade, calculate_total_grade, check_promotion_eligibility

def create_student_grades_sheet(class_id, output_path):
    """إنشاء ملف Excel لدرجات الطلاب في صف معين"""
    class_obj = Class.query.get(class_id)
    if not class_obj:
        return False
    
    # إنشاء ملف Excel جديد
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = f"درجات {class_obj.name}"
    
    # تنسيق الخلايا
    header_font = Font(name='Arial', size=12, bold=True)
    header_fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
    centered_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # الحصول على الطلاب والمواد والفترات
    students = Student.query.filter_by(class_id=class_id).all()
    subjects = Subject.query.all()
    periods = AcademicPeriod.query.filter_by(class_id=class_id).all()
    
    # إنشاء رأس الجدول
    headers = ["الرقم", "اسم الطالب", "الرقم الوطني"]
    
    # إضافة أعمدة المواد والفترات
    for subject in subjects:
        for period in periods:
            headers.append(f"{subject.name} - {period.name} - أعمال")
            headers.append(f"{subject.name} - {period.name} - امتحان")
            headers.append(f"{subject.name} - {period.name} - المجموع")
        headers.append(f"{subject.name} - المجموع")
        headers.append(f"{subject.name} - النتيجة")
    
    # إضافة أعمدة المجموع الكلي
    headers.extend(["المجموع الكلي", "النسبة المئوية", "النتيجة"])
    
    # كتابة رأس الجدول
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = centered_alignment
        cell.border = border
    
    # كتابة بيانات الطلاب
    for row, student in enumerate(students, 2):
        # بيانات الطالب الأساسية
        ws.cell(row=row, column=1, value=row-1).alignment = centered_alignment
        ws.cell(row=row, column=2, value=student.full_name).alignment = centered_alignment
        ws.cell(row=row, column=3, value=student.national_id).alignment = centered_alignment
        
        col = 4
        
        # درجات المواد
        for subject in subjects:
            subject_total = 0
            
            for period in periods:
                grade_info = calculate_subject_grade(student.id, subject.id, period.id)
                
                # أعمال الفصل
                ws.cell(row=row, column=col, value=grade_info['classwork']).alignment = centered_alignment
                col += 1
                
                # الامتحان
                ws.cell(row=row, column=col, value=grade_info['exam']).alignment = centered_alignment
                col += 1
                
                # المجموع
                ws.cell(row=row, column=col, value=grade_info['total']).alignment = centered_alignment
                col += 1
                
                subject_total += grade_info['total']
            
            # متوسط المادة
            subject_average = subject_total / len(periods)
            ws.cell(row=row, column=col, value=subject_average).alignment = centered_alignment
            col += 1
            
            # نتيجة المادة
            all_passed = all(calculate_subject_grade(student.id, subject.id, period.id)['is_passed'] for period in periods)
            result_cell = ws.cell(row=row, column=col, value="ناجح" if all_passed else "راسب")
            result_cell.alignment = centered_alignment
            
            # تلوين خلية النتيجة
            if all_passed:
                result_cell.fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
            else:
                result_cell.fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")
            
            col += 1
        
        # المجموع الكلي
        total_info = calculate_total_grade(student.id, class_id)
        if total_info:
            ws.cell(row=row, column=col, value=total_info['total_marks']).alignment = centered_alignment
            col += 1
            
            percentage_cell = ws.cell(row=row, column=col, value=total_info['percentage'])
            percentage_cell.alignment = centered_alignment
            percentage_cell.number_format = '0.00"%"'
            col += 1
        else:
            ws.cell(row=row, column=col, value=0).alignment = centered_alignment
            col += 1
            ws.cell(row=row, column=col, value=0).alignment = centered_alignment
            col += 1
        
        # النتيجة النهائية
        is_promoted = check_promotion_eligibility(student.id)
        result_cell = ws.cell(row=row, column=col, value="ناجح" if is_promoted else "راسب")
        result_cell.alignment = centered_alignment
        
        # تلوين خلية النتيجة النهائية
        if is_promoted:
            result_cell.fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
        else:
            result_cell.fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")
    
    # تعديل عرض الأعمدة
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15
    
    # حفظ الملف
    wb.save(output_path)
    
    return True

def create_exam_distribution_sheet(class_id, output_path):
    """إنشاء ملف Excel لتوزيع الطلاب على قاعات الامتحان"""
    class_obj = Class.query.get(class_id)
    if not class_obj:
        return False
    
    # إنشاء ملف Excel جديد
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = f"توزيع طلاب {class_obj.name}"
    
    # تنسيق الخلايا
    header_font = Font(name='Arial', size=12, bold=True)
    header_fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
    centered_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # الحصول على الطلاب
    students = Student.query.filter_by(class_id=class_id).order_by(Student.seat_number).all()
    
    # إنشاء رأس الجدول
    headers = ["الرقم", "اسم الطالب", "الرقم الوطني", "رقم الجلوس", "الرقم السري", "القاعة"]
    
    # كتابة رأس الجدول
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = centered_alignment
        cell.border = border
    
    # كتابة بيانات الطلاب
    for row, student in enumerate(students, 2):
        ws.cell(row=row, column=1, value=row-1).alignment = centered_alignment
        ws.cell(row=row, column=2, value=student.full_name).alignment = centered_alignment
        ws.cell(row=row, column=3, value=student.national_id).alignment = centered_alignment
        ws.cell(row=row, column=4, value=student.seat_number).alignment = centered_alignment
        ws.cell(row=row, column=5, value=student.secret_number).alignment = centered_alignment
        
        # تحديد القاعة بناءً على رقم الجلوس (مثال بسيط)
        hall_number = "غير محدد"
        if student.seat_number:
            seat_num = int(student.seat_number) if student.seat_number.isdigit() else 0
            hall_number = f"قاعة {(seat_num // 30) + 1}"
        
        ws.cell(row=row, column=6, value=hall_number).alignment = centered_alignment
    
    # تعديل عرض الأعمدة
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15
    
    # حفظ الملف
    wb.save(output_path)
    
    return True
