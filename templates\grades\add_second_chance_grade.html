{% extends 'base.html' %}

{% block title %}رصد درجة الدور الثاني - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}رصد درجة الدور الثاني: {{ subject.name }} - {{ period.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-graduate me-2"></i>
                    بيانات الطالب
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 30%">الاسم الكامل</th>
                        <td>{{ student.full_name }}</td>
                    </tr>
                    <tr>
                        <th>الرقم الوطني</th>
                        <td>{{ student.national_id }}</td>
                    </tr>
                    <tr>
                        <th>الصف</th>
                        <td>{{ student.class_obj.name }} ({{ student.class_obj.stage.name }})</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدرجة الحالية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>المادة:</strong> {{ subject.name }}</p>
                        <p><strong>الفترة:</strong> {{ period.name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>درجة أعمال الفصل:</strong> {{ grade.classwork_mark }}</p>
                        <p><strong>درجة امتحان الفصل:</strong> <span class="text-danger">{{ grade.exam_mark }}</span></p>
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> سيتم الاحتفاظ بدرجة أعمال الفصل كما هي، وسيتم استبدال درجة امتحان الفصل بدرجة امتحان الدور الثاني.
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    رصد درجة امتحان الدور الثاني
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('grades.add_second_chance_grade', grade_id=grade.id) }}" method="post">
                    <div class="mb-3">
                        <label for="second_chance_exam_mark" class="form-label">درجة امتحان الدور الثاني <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="second_chance_exam_mark" name="second_chance_exam_mark" min="0" max="{{ max_exam }}" step="0.5" value="{{ grade.second_chance_exam_mark or 0 }}" required>
                            <span class="input-group-text">من {{ max_exam }}</span>
                        </div>
                    </div>

                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6 class="card-title">ملخص الدرجة</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <p class="mb-1">أعمال الفصل:</p>
                                    <h5 class="text-primary">{{ grade.classwork_mark }}</h5>
                                </div>
                                <div class="col-md-4">
                                    <p class="mb-1">امتحان الدور الثاني:</p>
                                    <h5 id="examMark" class="text-primary">{{ grade.second_chance_exam_mark or 0 }}</h5>
                                </div>
                                <div class="col-md-4">
                                    <p class="mb-1">المجموع:</p>
                                    <h5 id="totalMark" class="text-primary">{{ (grade.classwork_mark + (grade.second_chance_exam_mark or 0))|round(2) }}</h5>
                                </div>
                            </div>

                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <p class="mb-1">النسبة المئوية:</p>
                                    <h5 id="percentage" class="text-primary">{{ ((grade.classwork_mark + (grade.second_chance_exam_mark or 0)) / (max_exam + grade.classwork_mark) * 100)|round(2) }}%</h5>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1">النتيجة:</p>
                                    <h5 id="result" class="{% if grade.is_passed %}text-success{% else %}text-danger{% endif %}">
                                        {% if grade.is_passed %}ناجح{% else %}راسب{% endif %}
                                    </h5>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('grades.student_second_chance', student_id=student.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            رجوع
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ الدرجة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener("DOMContentLoaded", function() {
        // حساب المجموع والنتيجة تلقائياً عند تغيير الدرجة
        var examMarkInput = document.getElementById("second_chance_exam_mark");
        var classworkMark = {{ grade.classwork_mark }};
        var maxExam = {{ max_exam }};

        function calculateGrade() {
            var examMark = parseFloat(examMarkInput.value) || 0;
            var total = classworkMark + examMark;
            var maxTotal = classworkMark + maxExam;
            var percentage = (total / maxTotal) * 100;
            var examPercentage = (examMark / maxExam) * 100;

            // عرض الدرجات
            document.getElementById("examMark").textContent = examMark.toFixed(2);
            document.getElementById("totalMark").textContent = total.toFixed(2);
            document.getElementById("percentage").textContent = percentage.toFixed(2) + "%";

            // تحديد النتيجة
            var resultElement = document.getElementById("result");

            if (examPercentage < 40) {
                resultElement.textContent = "راسب";
                resultElement.className = "text-danger";
            } else if (percentage < 50) {
                resultElement.textContent = "راسب";
                resultElement.className = "text-danger";
            } else {
                resultElement.textContent = "ناجح";
                resultElement.className = "text-success";
            }
        }

        examMarkInput.addEventListener("input", calculateGrade);
        calculateGrade(); // حساب الدرجات عند تحميل الصفحة
    });
</script>
{% endblock %}
