from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

# إنشاء كائن قاعدة البيانات
db = SQLAlchemy()

def init_db(app):
    """تهيئة قاعدة البيانات"""
    db.init_app(app)
    with app.app_context():
        db.create_all()

# نموذج الصف الدراسي
class Grade(db.Model):
    __tablename__ = 'grades'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    level = db.Column(db.Integer, nullable=False)  # 10, 11, 12 للصفوف الأول والثاني والثالث
    track = db.Column(db.String(20), nullable=False)  # علمي، أدبي، عام

    # العلاقات
    classes = db.relationship('Class', backref='grade', lazy=True)
    subjects = db.relationship('Subject', backref='grade', lazy=True)

    def __repr__(self):
        return f'<Grade {self.name}>'

# نموذج الفصل
class Class(db.Model):
    __tablename__ = 'classes'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    grade_id = db.Column(db.Integer, db.ForeignKey('grades.id'), nullable=False)
    academic_year = db.Column(db.String(20), nullable=False)

    # العلاقات
    students = db.relationship('Student', backref='class_', lazy=True)

    def __repr__(self):
        return f'<Class {self.name}>'

# نموذج الطالب
class Student(db.Model):
    __tablename__ = 'students'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    national_id = db.Column(db.String(12), unique=True, nullable=False)
    class_id = db.Column(db.Integer, db.ForeignKey('classes.id'), nullable=False)
    seat_number = db.Column(db.Integer, nullable=True)
    secret_number = db.Column(db.String(10), nullable=True)

    # العلاقات
    grades = db.relationship('StudentGrade', backref='student', lazy=True)

    def __repr__(self):
        return f'<Student {self.name}>'

# نموذج المادة الدراسية
class Subject(db.Model):
    __tablename__ = 'subjects'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    grade_id = db.Column(db.Integer, db.ForeignKey('grades.id'), nullable=False)
    total_mark = db.Column(db.Integer, nullable=False, default=100)

    def __repr__(self):
        return f'<Subject {self.name}>'

# نموذج الفترة الدراسية
class Period(db.Model):
    __tablename__ = 'periods'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)  # فصل أول، فصل ثاني، فترة أولى، فترة ثانية
    academic_year = db.Column(db.String(20), nullable=False)
    is_approved = db.Column(db.Boolean, default=False)  # حالة اعتماد الفترة

    # العلاقات
    grades = db.relationship('StudentGrade', backref='period', lazy=True)

    def __repr__(self):
        return f'<Period {self.name}>'

# نموذج درجات الطالب
class StudentGrade(db.Model):
    __tablename__ = 'student_grades'

    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'), nullable=False)
    period_id = db.Column(db.Integer, db.ForeignKey('periods.id'), nullable=False)
    classwork_mark = db.Column(db.Float, nullable=True)
    exam_mark = db.Column(db.Float, nullable=True)
    total_mark = db.Column(db.Float, nullable=True)
    is_passed = db.Column(db.Boolean, nullable=True)
    is_second_chance = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقة مع المادة
    subject = db.relationship('Subject', backref='grades', lazy='joined')

    def __repr__(self):
        return f'<StudentGrade {self.id}>'

    def calculate_total(self):
        """حساب المجموع الكلي للدرجة"""
        if self.classwork_mark is not None and self.exam_mark is not None:
            try:
                # تحويل الدرجات إلى أرقام للتأكد
                classwork_mark = float(self.classwork_mark)
                exam_mark = float(self.exam_mark)

                # التحقق من وجود المادة
                if self.subject is None:
                    # محاولة الحصول على المادة من قاعدة البيانات
                    from app import db
                    subject = db.session.query(Subject).filter_by(id=self.subject_id).first()
                    if subject is None:
                        print(f"لم يتم العثور على المادة برقم {self.subject_id}")
                        # استخدام قيمة افتراضية للدرجة الكلية
                        subject_total_mark = 100
                    else:
                        subject_total_mark = float(subject.total_mark)
                else:
                    # الحصول على الدرجة الكلية للمادة
                    subject_total_mark = float(self.subject.total_mark)

                # حساب الدرجة الكلية للفصل (نصف الدرجة الكلية للمادة)
                semester_total_mark = subject_total_mark / 2

                # التأكد من أن درجة الأعمال لا تتجاوز 40% من الدرجة الكلية للفصل
                max_classwork = semester_total_mark * 0.4  # مثلاً: 16 درجة إذا كانت الدرجة الكلية للفصل 40
                if classwork_mark > max_classwork:
                    classwork_mark = max_classwork
                    print(f"تم تعديل درجة الأعمال إلى الحد الأقصى: {max_classwork} (40% من الدرجة الكلية للفصل {semester_total_mark})")

                # التأكد من أن درجة الامتحان لا تتجاوز 60% من الدرجة الكلية للفصل
                max_exam = semester_total_mark * 0.6  # مثلاً: 24 درجة إذا كانت الدرجة الكلية للفصل 40
                if exam_mark > max_exam:
                    exam_mark = max_exam
                    print(f"تم تعديل درجة الامتحان إلى الحد الأقصى: {max_exam} (60% من الدرجة الكلية للفصل {semester_total_mark})")

                # التحقق من شرط الامتحان (40% من درجة الامتحان)
                min_exam_mark = max_exam * 0.4  # 9.6 درجة إذا كانت درجة الامتحان 24
                min_exam_requirement = exam_mark >= min_exam_mark

                # حساب المجموع الكلي
                total_points = classwork_mark + exam_mark

                # التحقق من شرط المجموع (50% من الدرجة الكلية للفصل)
                min_total_mark = semester_total_mark * 0.5  # مثلاً: 20 درجة إذا كانت الدرجة الكلية للفصل 40
                min_total_requirement = total_points >= min_total_mark

                # تحديد النجاح أو الرسوب
                self.is_passed = min_exam_requirement and min_total_requirement

                # تخزين المجموع الكلي (النقاط) فقط إذا تحقق شرط الامتحان
                if min_exam_requirement:
                    self.total_mark = total_points
                else:
                    # إذا لم يتحقق شرط الامتحان، نضع علامة "-" (نخزنها كقيمة 0)
                    self.total_mark = 0



            except (ValueError, TypeError, ZeroDivisionError, AttributeError) as e:
                # في حالة حدوث خطأ، نسجل الخطأ ونعيد None
                print(f"خطأ في حساب المجموع: {e}")
                print(f"classwork_mark: {self.classwork_mark}, exam_mark: {self.exam_mark}, subject_id: {self.subject_id}")
                # استخدام قيم افتراضية للحساب
                try:
                    classwork_mark = float(self.classwork_mark)
                    exam_mark = float(self.exam_mark)
                    # استخدام قيمة افتراضية للدرجة الكلية
                    subject_total_mark = 100

                    # حساب الدرجة الكلية للفصل (نصف الدرجة الكلية للمادة)
                    semester_total_mark = subject_total_mark / 2

                    # التأكد من أن درجة الأعمال لا تتجاوز 40% من الدرجة الكلية للفصل
                    max_classwork = semester_total_mark * 0.4  # مثلاً: 16 درجة إذا كانت الدرجة الكلية للفصل 40
                    if classwork_mark > max_classwork:
                        classwork_mark = max_classwork
                        print(f"تم تعديل درجة الأعمال إلى الحد الأقصى: {max_classwork} (40% من الدرجة الكلية للفصل {semester_total_mark})")

                    # التأكد من أن درجة الامتحان لا تتجاوز 60% من الدرجة الكلية للفصل
                    max_exam = semester_total_mark * 0.6  # مثلاً: 24 درجة إذا كانت الدرجة الكلية للفصل 40
                    if exam_mark > max_exam:
                        exam_mark = max_exam
                        print(f"تم تعديل درجة الامتحان إلى الحد الأقصى: {max_exam} (60% من الدرجة الكلية للفصل {semester_total_mark})")

                    # التحقق من شرط الامتحان (40% من درجة الامتحان)
                    min_exam_mark = max_exam * 0.4  # 9.6 درجة إذا كانت درجة الامتحان 24
                    min_exam_requirement = exam_mark >= min_exam_mark

                    # حساب المجموع الكلي
                    total_points = classwork_mark + exam_mark

                    # التحقق من شرط المجموع (50% من الدرجة الكلية للفصل)
                    min_total_mark = semester_total_mark * 0.5  # مثلاً: 20 درجة إذا كانت الدرجة الكلية للفصل 40
                    min_total_requirement = total_points >= min_total_mark

                    # تحديد النجاح أو الرسوب
                    self.is_passed = min_exam_requirement and min_total_requirement

                    # تخزين المجموع الكلي (النقاط) فقط إذا تحقق شرط الامتحان
                    if min_exam_requirement:
                        self.total_mark = total_points
                    else:
                        # إذا لم يتحقق شرط الامتحان، نضع علامة "-" (نخزنها كقيمة 0)
                        self.total_mark = 0





                    print(f"تم استخدام قيم افتراضية للحساب: total_mark = {self.total_mark}")
                except Exception as e2:
                    print(f"خطأ في الحساب باستخدام القيم الافتراضية: {e2}")
                    self.total_mark = None
                    self.is_passed = False

        return self.total_mark
