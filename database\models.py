from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timezone

db = SQLAlchemy()

# جدول العلاقة بين الطلاب والمواد الدراسية
student_subject = db.Table('student_subject',
    db.<PERSON>umn('student_id', db.<PERSON><PERSON>, db.<PERSON>('student.id'), primary_key=True),
    db.<PERSON>umn('subject_id', db.Integer, db.<PERSON><PERSON>('subject.id'), primary_key=True)
)

class Student(db.Model):
    """نموذج بيانات الطالب"""
    id = db.Column(db.Integer, primary_key=True)
    full_name = db.Column(db.String(100), nullable=False)
    national_id = db.Column(db.String(12), unique=True, nullable=False)
    secret_number = db.Column(db.String(20), unique=True, nullable=False)
    seat_number = db.Column(db.String(20), nullable=True)

    # العلاقات
    class_id = db.Column(db.Integer, db.<PERSON><PERSON>('school_class.id'), nullable=False)
    grades = db.relationship('Grade', backref='student', lazy=True)

    def __repr__(self):
        return f'<Student {self.full_name}>'

class EducationalStage(db.Model):
    """نموذج المرحلة الدراسية (أساسي - إعدادي - ثانوي)"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text, nullable=True)  # وصف المرحلة
    order = db.Column(db.Integer, default=0)  # ترتيب المرحلة

    # العلاقات
    classes = db.relationship('Class', backref='stage', lazy=True)
    stage_subjects = db.relationship('StageSubject', backref='stage', lazy=True)

    def __repr__(self):
        return f'<EducationalStage {self.name}>'

class Class(db.Model):
    """نموذج الصف الدراسي"""
    __tablename__ = 'school_class'  # تغيير اسم الجدول لتجنب استخدام كلمة محجوزة
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    specialization = db.Column(db.String(20), nullable=True)  # التخصص: عام، علمي، أدبي
    has_unified_exam = db.Column(db.Boolean, default=False)  # هل يخضع لامتحان موحد
    evaluation_type = db.Column(db.String(20), default='semester')  # نوع التقييم: semester (فصلي)، period (فترات)
    order = db.Column(db.Integer, default=0)  # ترتيب الصف ضمن المرحلة
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # العلاقات
    stage_id = db.Column(db.Integer, db.ForeignKey('educational_stage.id'), nullable=False)
    students = db.relationship('Student', backref='class_obj', lazy=True)
    academic_periods = db.relationship('AcademicPeriod', backref='class_obj', lazy=True)

    def __repr__(self):
        if self.specialization:
            return f'<Class {self.name} ({self.specialization})>'
        return f'<Class {self.name}>'

class AcademicPeriod(db.Model):
    """نموذج الفترة الدراسية (فصل أول - فصل ثاني - فترة أولى/ثانية/ثالثة)"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    period_type = db.Column(db.String(20), default='semester')  # نوع الفترة: semester (فصل دراسي)، period (فترة امتحانية)، final (نهائي)
    weight = db.Column(db.Float, default=1.0)  # وزن الفترة في حساب المعدل النهائي
    is_final = db.Column(db.Boolean, default=False)  # هل هي فترة نهائية (للامتحانات الموحدة)
    start_date = db.Column(db.Date, nullable=True)  # تاريخ بداية الفترة
    end_date = db.Column(db.Date, nullable=True)  # تاريخ نهاية الفترة
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # العلاقات
    class_id = db.Column(db.Integer, db.ForeignKey('school_class.id'), nullable=False)
    grades = db.relationship('Grade', backref='period', lazy=True)

    def __repr__(self):
        return f'<AcademicPeriod {self.name}>'

class Subject(db.Model):
    """نموذج المادة الدراسية الأساسي"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(20), nullable=True)  # رمز المادة
    description = db.Column(db.Text, nullable=True)  # وصف المادة

    # العلاقات
    stage_subjects = db.relationship('StageSubject', backref='subject', lazy=True)

    def __repr__(self):
        return f'<Subject {self.name}>'

class StageSubject(db.Model):
    """نموذج ربط المادة الدراسية بالمرحلة مع تحديد الدرجات وطرق التقييم الافتراضية للمرحلة"""
    __tablename__ = 'stage_subject'
    id = db.Column(db.Integer, primary_key=True)
    subject_id = db.Column(db.Integer, db.ForeignKey('subject.id'), nullable=False)
    stage_id = db.Column(db.Integer, db.ForeignKey('educational_stage.id'), nullable=False)
    total_mark = db.Column(db.Float, nullable=False, default=100.0)  # الدرجة الكلية للمادة
    classwork_percentage = db.Column(db.Float, nullable=False, default=40.0)  # نسبة أعمال الفصل (%)
    exam_percentage = db.Column(db.Float, nullable=False, default=60.0)  # نسبة الامتحان (%)
    passing_percentage = db.Column(db.Float, nullable=False, default=50.0)  # نسبة النجاح (%)
    min_exam_percentage = db.Column(db.Float, nullable=False, default=40.0)  # الحد الأدنى لنسبة النجاح في الامتحان (%)
    daily_evaluation_only = db.Column(db.Boolean, default=False)  # للصفوف 1-3 فقط
    is_active = db.Column(db.Boolean, default=True)  # هل المادة مفعلة في هذه المرحلة
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # العلاقات
    class_subjects = db.relationship('ClassSubject', backref='stage_subject', lazy=True)

    # قيود فريدة لضمان عدم تكرار المادة في نفس المرحلة
    __table_args__ = (db.UniqueConstraint('subject_id', 'stage_id', name='_subject_stage_uc'),)

    def __repr__(self):
        return f'<StageSubject {self.subject_id} - {self.stage_id}>'

class ClassSubject(db.Model):
    """نموذج ربط المادة الدراسية بالصف مع تحديد الدرجات وطرق التقييم الخاصة بالصف"""
    __tablename__ = 'class_subject'
    id = db.Column(db.Integer, primary_key=True)
    stage_subject_id = db.Column(db.Integer, db.ForeignKey('stage_subject.id'), nullable=False)
    class_id = db.Column(db.Integer, db.ForeignKey('school_class.id'), nullable=False)
    total_mark = db.Column(db.Float, nullable=False, default=100.0)  # الدرجة الكلية للمادة
    classwork_percentage = db.Column(db.Float, nullable=False, default=40.0)  # نسبة أعمال الفصل (%)
    exam_percentage = db.Column(db.Float, nullable=False, default=60.0)  # نسبة الامتحان (%)
    passing_percentage = db.Column(db.Float, nullable=False, default=50.0)  # نسبة النجاح (%)
    min_exam_percentage = db.Column(db.Float, nullable=False, default=40.0)  # الحد الأدنى لنسبة النجاح في الامتحان (%)
    daily_evaluation_only = db.Column(db.Boolean, default=False)  # للصفوف 1-3 فقط
    is_active = db.Column(db.Boolean, default=True)  # هل المادة مفعلة في هذا الصف
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # العلاقات
    grades = db.relationship('Grade', backref='class_subject', lazy=True)
    class_obj = db.relationship('Class', backref='class_subjects', lazy=True)

    # قيود فريدة لضمان عدم تكرار المادة في نفس الصف
    __table_args__ = (db.UniqueConstraint('stage_subject_id', 'class_id', name='_subject_class_uc'),)

    def __repr__(self):
        return f'<ClassSubject {self.stage_subject_id} - {self.class_id}>'

class Grade(db.Model):
    """نموذج درجات الطالب"""
    id = db.Column(db.Integer, primary_key=True)
    classwork_mark = db.Column(db.Float, nullable=False)  # درجة أعمال الفصل
    exam_mark = db.Column(db.Float, nullable=False)  # درجة الامتحان
    second_chance_exam_mark = db.Column(db.Float, nullable=True)  # درجة امتحان الدور الثاني
    is_passed = db.Column(db.Boolean, default=False)  # حالة النجاح
    total_mark = db.Column(db.Float, nullable=True)  # الدرجة الكلية (تحسب تلقائياً)
    notes = db.Column(db.Text, nullable=True)  # ملاحظات
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # العلاقات
    student_id = db.Column(db.Integer, db.ForeignKey('student.id'), nullable=False)
    class_subject_id = db.Column(db.Integer, db.ForeignKey('class_subject.id'), nullable=False)
    period_id = db.Column(db.Integer, db.ForeignKey('academic_period.id'), nullable=False)

    # قيود فريدة لضمان عدم تكرار الدرجة لنفس الطالب ونفس المادة ونفس الفترة
    __table_args__ = (db.UniqueConstraint('student_id', 'class_subject_id', 'period_id', name='_student_class_subject_period_uc'),)

    def __repr__(self):
        return f'<Grade {self.student_id} - {self.class_subject_id}>'

class ExamHall(db.Model):
    """نموذج قاعة الامتحان"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    capacity = db.Column(db.Integer, nullable=False)

    def __repr__(self):
        return f'<ExamHall {self.name}>'
