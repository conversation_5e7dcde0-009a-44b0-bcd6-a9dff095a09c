import os

class Config:
    # إعدادات التطبيق
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'مفتاح_سري_افتراضي_للتطوير'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.abspath(os.path.dirname(__file__)), 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات التطبيق
    APP_NAME = "نظام إدارة درجات الطلبة"
    APP_VERSION = "1.0.0"
    
    # إعدادات الملفات
    UPLOAD_FOLDER = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'static', 'uploads')
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'pdf'}
    
    # إعدادات التقارير
    REPORTS_FOLDER = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'static', 'reports')
