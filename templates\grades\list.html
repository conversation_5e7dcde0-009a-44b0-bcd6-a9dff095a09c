{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">الصفوف الدراسية</h1>
        <a href="{{ url_for('add_grade') }}" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-plus ml-1"></i> إضافة صف دراسي
        </a>
    </div>
    
    {% if grades %}
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
            <thead>
                <tr class="bg-gray-100">
                    <th class="py-3 px-4 border-b text-right">#</th>
                    <th class="py-3 px-4 border-b text-right">اسم الصف</th>
                    <th class="py-3 px-4 border-b text-right">المستوى</th>
                    <th class="py-3 px-4 border-b text-right">المسار</th>
                    <th class="py-3 px-4 border-b text-center">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for grade in grades %}
                <tr class="hover:bg-gray-50">
                    <td class="py-3 px-4 border-b">{{ loop.index }}</td>
                    <td class="py-3 px-4 border-b">{{ grade.name }}</td>
                    <td class="py-3 px-4 border-b">{{ grade.level }}</td>
                    <td class="py-3 px-4 border-b">{{ grade.track }}</td>
                    <td class="py-3 px-4 border-b text-center">
                        <a href="{{ url_for('edit_grade', id=grade.id) }}" class="bg-blue-500 hover:bg-blue-600 text-white text-sm py-1 px-3 rounded transition-colors mx-1">
                            <i class="fas fa-edit ml-1"></i> تعديل
                        </a>
                        <a href="{{ url_for('list_classes', grade_id=grade.id) }}" class="bg-purple-500 hover:bg-purple-600 text-white text-sm py-1 px-3 rounded transition-colors mx-1">
                            <i class="fas fa-list ml-1"></i> الفصول
                        </a>
                        <a href="{{ url_for('list_subjects', grade_id=grade.id) }}" class="bg-teal-500 hover:bg-teal-600 text-white text-sm py-1 px-3 rounded transition-colors mx-1">
                            <i class="fas fa-book ml-1"></i> المواد
                        </a>
                        <form method="POST" action="{{ url_for('delete_grade', id=grade.id) }}" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الصف الدراسي؟');">
                            <button type="submit" class="bg-red-500 hover:bg-red-600 text-white text-sm py-1 px-3 rounded transition-colors mx-1">
                                <i class="fas fa-trash ml-1"></i> حذف
                            </button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="bg-gray-100 p-6 rounded-lg text-center">
        <p class="text-gray-500">لا توجد صفوف دراسية مسجلة</p>
        <a href="{{ url_for('add_grade') }}" class="mt-4 inline-block bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-plus ml-1"></i> إضافة صف دراسي
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}
