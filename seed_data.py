from flask import Flask
from models import db, Grade, Class, Subject, Period, Student
from run import app

def seed_database():
    """إضافة بيانات تجريبية إلى قاعدة البيانات"""
    
    print("بدء إضافة البيانات التجريبية...")
    
    # إضافة الصفوف الدراسية
    grade1 = Grade(name="الصف الأول الثانوي", level=10, track="عام")
    grade2 = Grade(name="الصف الثاني الثانوي", level=11, track="علمي")
    grade3 = Grade(name="الصف الثاني الثانوي", level=11, track="أدبي")
    
    db.session.add_all([grade1, grade2, grade3])
    db.session.commit()
    
    print("تم إضافة الصفوف الدراسية.")
    
    # إضافة الفصول
    class1 = Class(name="1/أ", grade_id=grade1.id, academic_year="2023-2024")
    class2 = Class(name="2/علمي", grade_id=grade2.id, academic_year="2023-2024")
    class3 = Class(name="2/أدبي", grade_id=grade3.id, academic_year="2023-2024")
    
    db.session.add_all([class1, class2, class3])
    db.session.commit()
    
    print("تم إضافة الفصول.")
    
    # إضافة المواد الدراسية
    # مواد الصف الأول الثانوي
    subject1 = Subject(name="اللغة العربية", grade_id=grade1.id, total_mark=160)
    subject2 = Subject(name="اللغة الإنجليزية", grade_id=grade1.id, total_mark=120)
    subject3 = Subject(name="الرياضيات", grade_id=grade1.id, total_mark=160)
    subject4 = Subject(name="الفيزياء", grade_id=grade1.id, total_mark=80)
    subject5 = Subject(name="الكيمياء", grade_id=grade1.id, total_mark=80)
    
    # مواد الصف الثاني الثانوي (علمي)
    subject6 = Subject(name="اللغة العربية", grade_id=grade2.id, total_mark=160)
    subject7 = Subject(name="اللغة الإنجليزية", grade_id=grade2.id, total_mark=120)
    subject8 = Subject(name="الرياضيات", grade_id=grade2.id, total_mark=160)
    subject9 = Subject(name="الفيزياء", grade_id=grade2.id, total_mark=120)
    subject10 = Subject(name="الكيمياء", grade_id=grade2.id, total_mark=120)
    
    # مواد الصف الثاني الثانوي (أدبي)
    subject11 = Subject(name="اللغة العربية", grade_id=grade3.id, total_mark=160)
    subject12 = Subject(name="اللغة الإنجليزية", grade_id=grade3.id, total_mark=120)
    subject13 = Subject(name="التاريخ", grade_id=grade3.id, total_mark=120)
    subject14 = Subject(name="الجغرافيا", grade_id=grade3.id, total_mark=120)
    subject15 = Subject(name="الفلسفة", grade_id=grade3.id, total_mark=80)
    
    db.session.add_all([
        subject1, subject2, subject3, subject4, subject5,
        subject6, subject7, subject8, subject9, subject10,
        subject11, subject12, subject13, subject14, subject15
    ])
    db.session.commit()
    
    print("تم إضافة المواد الدراسية.")
    
    # إضافة الفترات الدراسية
    period1 = Period(name="الفصل الأول", academic_year="2023-2024", is_approved=False)
    period2 = Period(name="الفصل الثاني", academic_year="2023-2024", is_approved=False)
    
    db.session.add_all([period1, period2])
    db.session.commit()
    
    print("تم إضافة الفترات الدراسية.")
    
    # إضافة الطلاب
    # طلاب الصف الأول الثانوي
    student1 = Student(name="أحمد محمد علي", national_id="123456789012", class_id=class1.id)
    student2 = Student(name="محمد أحمد محمود", national_id="123456789013", class_id=class1.id)
    student3 = Student(name="فاطمة علي حسن", national_id="123456789014", class_id=class1.id)
    
    # طلاب الصف الثاني الثانوي (علمي)
    student4 = Student(name="علي حسن محمد", national_id="123456789015", class_id=class2.id)
    student5 = Student(name="سارة أحمد محمود", national_id="123456789016", class_id=class2.id)
    
    # طلاب الصف الثاني الثانوي (أدبي)
    student6 = Student(name="خالد محمد علي", national_id="123456789017", class_id=class3.id)
    student7 = Student(name="نورا علي حسن", national_id="123456789018", class_id=class3.id)
    
    db.session.add_all([student1, student2, student3, student4, student5, student6, student7])
    db.session.commit()
    
    print("تم إضافة الطلاب.")
    
    print("تم إضافة جميع البيانات التجريبية بنجاح.")

if __name__ == "__main__":
    with app.app_context():
        seed_database()
