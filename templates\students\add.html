{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">إضافة طالب جديد</h1>
        <a href="{{ url_for('list_students') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-arrow-right ml-1"></i> العودة إلى القائمة
        </a>
    </div>
    
    <form method="POST" action="{{ url_for('add_student') }}" class="max-w-2xl mx-auto">
        <div class="mb-4">
            <label for="name" class="block text-gray-700 font-medium mb-2">اسم الطالب <span class="text-red-500">*</span></label>
            <input type="text" id="name" name="name" required class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        </div>
        
        <div class="mb-4">
            <label for="national_id" class="block text-gray-700 font-medium mb-2">الرقم الوطني <span class="text-red-500">*</span></label>
            <input type="text" id="national_id" name="national_id" required pattern="[0-9]{12}" maxlength="12" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <p class="text-sm text-gray-500 mt-1">يجب أن يتكون الرقم الوطني من 12 رقم</p>
        </div>
        
        <div class="mb-6">
            <label for="class_id" class="block text-gray-700 font-medium mb-2">الفصل <span class="text-red-500">*</span></label>
            <select id="class_id" name="class_id" required class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="" disabled selected>اختر الفصل</option>
                {% for class in classes %}
                <option value="{{ class.id }}">{{ class.name }} - {{ class.grade.name }} ({{ class.grade.track }})</option>
                {% endfor %}
            </select>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors">
                <i class="fas fa-save ml-1"></i> حفظ
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script>
    // التحقق من صحة الرقم الوطني
    document.getElementById('national_id').addEventListener('input', function() {
        this.value = this.value.replace(/[^0-9]/g, '');
        
        if (this.value.length > 12) {
            this.value = this.value.slice(0, 12);
        }
    });
</script>
{% endblock %}
