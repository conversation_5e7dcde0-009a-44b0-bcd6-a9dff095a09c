{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <h1 class="text-2xl font-bold text-blue-600 mb-6">رصد درجات الطلاب</h1>
    
    <!-- نموذج اختيار الفصل والمادة والفترة -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold mb-4">اختر الفصل والمادة والفترة</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="class-select" class="block text-gray-700 font-medium mb-2">الفصل</label>
                <select id="class-select" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="" disabled selected>اختر الفصل</option>
                    {% for class in classes %}
                    <option value="{{ class.id }}">{{ class.name }} - {{ class.grade.name }} ({{ class.grade.track }})</option>
                    {% endfor %}
                </select>
            </div>

            <div>
                <label for="subject-select" class="block text-gray-700 font-medium mb-2">المادة</label>
                <select id="subject-select" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled>
                    <option value="" disabled selected>اختر المادة</option>
                </select>
            </div>

            <div>
                <label for="period-select" class="block text-gray-700 font-medium mb-2">الفترة</label>
                <select id="period-select" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled>
                    <option value="" disabled selected>اختر الفترة</option>
                    {% for period in periods %}
                    <option value="{{ period.id }}">{{ period.name }} - {{ period.academic_year }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <!-- خيارات إضافية -->
        <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- طريقة رصد الفصول -->
            <div>
                <label class="block text-gray-700 font-medium mb-2">طريقة رصد الفصول</label>
                <div class="flex space-x-4 space-x-reverse">
                    <label class="inline-flex items-center">
                        <input type="radio" name="semester-type" value="combined" class="form-radio" checked>
                        <span class="mr-2">فصل مجمع</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="semester-type" value="separate" class="form-radio">
                        <span class="mr-2">فصول منفصلة</span>
                    </label>
                </div>
            </div>

            <!-- نوع الدرجات المراد رصدها -->
            <div>
                <label class="block text-gray-700 font-medium mb-2">نوع الدرجات المراد رصدها</label>
                <div class="flex space-x-4 space-x-reverse">
                    <label class="inline-flex items-center">
                        <input type="radio" name="grade-type" value="both" class="form-radio" checked>
                        <span class="mr-2">كلاهما</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="grade-type" value="classwork" class="form-radio">
                        <span class="mr-2">أعمال الفصل</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="grade-type" value="exam" class="form-radio">
                        <span class="mr-2">الامتحان</span>
                    </label>
                </div>
            </div>

            <!-- طريقة رصد الدرجات -->
            <div>
                <label class="block text-gray-700 font-medium mb-2">طريقة رصد الدرجات</label>
                <div class="flex space-x-4 space-x-reverse">
                    <label class="inline-flex items-center">
                        <input type="radio" name="record-method" value="name" class="form-radio" checked>
                        <span class="mr-2">الاسم</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="record-method" value="secret-number" class="form-radio">
                        <span class="mr-2">الرقم السري</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="record-method" value="seat-number" class="form-radio">
                        <span class="mr-2">رقم الجلوس</span>
                    </label>
                </div>
            </div>
        </div>

        <div class="mt-6 text-center">
            <button id="load-students-btn" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors" disabled>
                <i class="fas fa-search ml-1"></i> عرض الطلاب
            </button>
        </div>
    </div>

    <!-- جدول رصد الدرجات -->
    <div id="grades-container" class="hidden">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">رصد درجات <span id="subject-name"></span></h2>
            <div class="text-sm text-gray-600">
                الدرجة الكلية: <span id="total-mark" class="font-semibold"></span>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="py-3 px-4 border-b text-right" rowspan="2">#</th>
                        <th class="py-3 px-4 border-b text-right student-id-header" rowspan="2">الرقم السري</th>
                        <th class="py-3 px-4 border-b text-right student-seat-header" rowspan="2">رقم الجلوس</th>
                        <th class="py-3 px-4 border-b text-right student-name-header" rowspan="2">الاسم</th>
                        <th class="py-3 px-4 border-b text-center" colspan="3">الدرجة</th>
                    </tr>
                    <tr class="bg-gray-50">
                        <th class="py-2 px-4 border-b text-center classwork-header">أعمال الفصل (40%)</th>
                        <th class="py-2 px-4 border-b text-center exam-header">الامتحان (60%)</th>
                        <th class="py-2 px-4 border-b text-center">المجموع</th>
                    </tr>
                </thead>
                <tbody id="grades-table-body">
                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                </tbody>
            </table>
        </div>

        <div class="mt-6 flex justify-end">
            <button id="save-grades-btn" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-6 rounded transition-colors">
                <i class="fas fa-save ml-1"></i> حفظ الدرجات
            </button>
        </div>
    </div>

    <!-- رسالة عدم وجود طلاب -->
    <div id="no-students-message" class="hidden text-center py-10 text-gray-500">
        <i class="fas fa-users text-5xl mb-4 text-gray-300"></i>
        <p>لا يوجد طلاب مسجلين في هذا الفصل</p>
    </div>

    <!-- مؤشر التحميل -->
    <div id="loading-indicator" class="hidden text-center py-10">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">جاري تحميل البيانات...</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/grades_record.js') }}"></script>
{% endblock %}
