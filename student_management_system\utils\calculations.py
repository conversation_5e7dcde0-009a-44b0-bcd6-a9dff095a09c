from database.models import Grade, Subject, AcademicPeriod

def calculate_subject_grade(student_id, subject_id, period_id):
    """حساب درجة الطالب في مادة معينة لفترة معينة"""
    grade = Grade.query.filter_by(
        student_id=student_id,
        subject_id=subject_id,
        period_id=period_id
    ).first()
    
    if not grade:
        return {
            'classwork': 0,
            'exam': 0,
            'total': 0,
            'is_passed': False
        }
    
    # استخدام درجة امتحان الدور الثاني إذا كانت موجودة
    exam_mark = grade.second_chance_exam_mark if grade.second_chance_exam_mark is not None else grade.exam_mark
    
    return {
        'classwork': grade.classwork_mark,
        'exam': exam_mark,
        'total': grade.classwork_mark + exam_mark,
        'is_passed': grade.is_passed
    }

def calculate_total_grade(student_id, class_id):
    """حساب المجموع الكلي للطالب في جميع المواد"""
    from database.models import db, Student, Class
    
    student = Student.query.get(student_id)
    if not student or student.class_id != class_id:
        return None
    
    # الحصول على جميع المواد للصف
    subjects = Subject.query.all()
    
    # الحصول على جميع الفترات للصف
    periods = AcademicPeriod.query.filter_by(class_id=class_id).all()
    
    total_marks = 0
    total_possible_marks = 0
    
    for subject in subjects:
        subject_total = 0
        
        for period in periods:
            grade_info = calculate_subject_grade(student_id, subject.id, period.id)
            subject_total += grade_info['total']
        
        # حساب متوسط درجة المادة عبر جميع الفترات
        subject_average = subject_total / len(periods)
        total_marks += subject_average
        total_possible_marks += subject.total_mark
    
    return {
        'total_marks': total_marks,
        'total_possible': total_possible_marks,
        'percentage': (total_marks / total_possible_marks) * 100 if total_possible_marks > 0 else 0
    }

def check_promotion_eligibility(student_id):
    """التحقق من أهلية الطالب للترقية للصف التالي"""
    from database.models import db, Student, Grade
    
    student = Student.query.get(student_id)
    if not student:
        return False
    
    # الحصول على جميع درجات الطالب
    grades = Grade.query.filter_by(student_id=student_id).all()
    
    # التحقق من وجود أي مادة راسب فيها الطالب
    failed_subjects = [grade for grade in grades if not grade.is_passed]
    
    # الطالب مؤهل للترقية إذا نجح في جميع المواد
    return len(failed_subjects) == 0

def calculate_class_statistics(class_id):
    """حساب إحصائيات الصف (نسبة النجاح، متوسط الدرجات، إلخ)"""
    from database.models import db, Student
    
    students = Student.query.filter_by(class_id=class_id).all()
    
    total_students = len(students)
    if total_students == 0:
        return {
            'total_students': 0,
            'passed_students': 0,
            'failed_students': 0,
            'pass_rate': 0,
            'average_grade': 0
        }
    
    passed_students = sum(1 for student in students if check_promotion_eligibility(student.id))
    
    # حساب متوسط الدرجات
    total_grades = 0
    for student in students:
        grade_info = calculate_total_grade(student.id, class_id)
        if grade_info:
            total_grades += grade_info['percentage']
    
    average_grade = total_grades / total_students if total_students > 0 else 0
    
    return {
        'total_students': total_students,
        'passed_students': passed_students,
        'failed_students': total_students - passed_students,
        'pass_rate': (passed_students / total_students) * 100 if total_students > 0 else 0,
        'average_grade': average_grade
    }
