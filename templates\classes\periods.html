{% extends 'base.html' %}

{% block title %}الفترات الدراسية - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}الفترات الدراسية للصف: {{ class_obj.name }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <span>قائمة الفترات الدراسية</span>
                <a href="{{ url_for('classes.add_period', class_id=class_obj.id) }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus me-1"></i>
                    إضافة فترة جديدة
                </a>
            </div>
            <div class="card-body">
                {% if periods %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم الفترة</th>
                                <th>عدد الدرجات المسجلة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for period in periods %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ period.name }}</td>
                                <td>{{ period.grades|length }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('classes.edit_period', period_id=period.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deletePeriodModal{{ period.id }}" data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    
                                    <!-- Modal for Delete Confirmation -->
                                    <div class="modal fade" id="deletePeriodModal{{ period.id }}" tabindex="-1" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">تأكيد الحذف</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>هل أنت متأكد من حذف الفترة: <strong>{{ period.name }}</strong>؟</p>
                                                    {% if period.grades %}
                                                    <div class="alert alert-warning">
                                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                                        لا يمكن حذف هذه الفترة لأنها تحتوي على {{ period.grades|length }} درجة/درجات مسجلة.
                                                    </div>
                                                    {% else %}
                                                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
                                                    {% endif %}
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                    {% if not period.grades %}
                                                    <form action="{{ url_for('classes.delete_period', period_id=period.id) }}" method="post" class="d-inline">
                                                        <button type="submit" class="btn btn-danger">حذف</button>
                                                    </form>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد فترات دراسية مسجلة لهذا الصف حالياً.
                    <a href="{{ url_for('classes.add_period', class_id=class_obj.id) }}" class="alert-link">إضافة فترة جديدة</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="mt-4">
    <a href="{{ url_for('classes.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>
        رجوع إلى الصفوف
    </a>
</div>
{% endblock %}
