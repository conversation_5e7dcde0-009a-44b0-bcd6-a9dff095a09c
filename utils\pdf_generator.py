import os
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from reportlab.lib.units import inch, cm
from database.models import Student, Grade, Subject, AcademicPeriod, Class
from utils.calculations import calculate_subject_grade, calculate_total_grade, check_promotion_eligibility

# تسجيل الخطوط العربية
try:
    pdfmetrics.registerFont(TTFont('Arabic', 'static/fonts/NotoSansArabic-Regular.ttf'))
    pdfmetrics.registerFont(TTFont('ArabicBold', 'static/fonts/NotoSansArabic-Bold.ttf'))
except:
    # استخدام الخطوط الافتراضية إذا لم تتوفر الخطوط العربية
    pass

def create_student_report(student_id, output_path):
    """إنشاء تقرير درجات للطالب"""
    student = Student.query.get(student_id)
    if not student:
        return False

    # إنشاء ملف PDF
    doc = SimpleDocTemplate(
        output_path,
        pagesize=A4,
        rightMargin=72,
        leftMargin=72,
        topMargin=72,
        bottomMargin=72
    )

    # إنشاء نمط للنص العربي
    styles = getSampleStyleSheet()
    arabic_style = ParagraphStyle(
        'Arabic',
        parent=styles['Normal'],
        fontName='Arabic',
        alignment=1,  # وسط
        leading=14
    )

    arabic_bold_style = ParagraphStyle(
        'ArabicBold',
        parent=styles['Heading1'],
        fontName='ArabicBold',
        alignment=1,  # وسط
        leading=16
    )

    # إنشاء محتوى التقرير
    elements = []

    # عنوان التقرير
    title = Paragraph("تقرير درجات الطالب", arabic_bold_style)
    elements.append(title)

    # بيانات الطالب
    student_info = [
        ["اسم الطالب:", student.full_name],
        ["الرقم الوطني:", student.national_id],
        ["الرقم السري:", student.secret_number],
        ["رقم الجلوس:", student.seat_number or "غير محدد"],
        ["الصف:", student.class_obj.name]
    ]

    student_table = Table(student_info, colWidths=[100, 300])
    student_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
        ('ALIGNMENT', (0, 0), (0, -1), 'RIGHT'),
        ('ALIGNMENT', (1, 0), (1, -1), 'RIGHT'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey)
    ]))

    elements.append(student_table)
    elements.append(Paragraph("<br/><br/>", arabic_style))

    # الحصول على المواد والفترات
    subjects = Subject.query.all()
    periods = AcademicPeriod.query.filter_by(class_id=student.class_id).all()

    # إنشاء جدول الدرجات
    grades_data = [["المادة"]]

    # إضافة أسماء الفترات
    for period in periods:
        grades_data[0].extend([period.name, "", ""])

    grades_data[0].extend(["المجموع", "النتيجة"])

    # إضافة عناوين الأعمدة الفرعية
    sub_header = [""]
    for _ in periods:
        sub_header.extend(["أعمال", "امتحان", "المجموع"])
    sub_header.extend(["", ""])

    grades_data.append(sub_header)

    # إضافة بيانات الدرجات
    for subject in subjects:
        subject_row = [subject.name]
        subject_total = 0

        for period in periods:
            grade_info = calculate_subject_grade(student.id, subject.id, period.id)
            subject_row.extend([
                f"{grade_info['classwork']:.1f}",
                f"{grade_info['exam']:.1f}",
                f"{grade_info['total']:.1f}"
            ])
            subject_total += grade_info['total']

        # حساب متوسط المادة
        subject_average = subject_total / len(periods)
        subject_row.append(f"{subject_average:.1f}")

        # تحديد النتيجة
        all_passed = all(calculate_subject_grade(student.id, subject.id, period.id)['is_passed'] for period in periods)
        subject_row.append("ناجح" if all_passed else "راسب")

        grades_data.append(subject_row)

    # إضافة المجموع الكلي
    total_info = calculate_total_grade(student.id, student.class_id)
    if total_info:
        total_row = ["المجموع الكلي"]
        for _ in range(len(periods) * 3):
            total_row.append("")
        total_row.extend([
            f"{total_info['total_marks']:.1f}",
            f"{total_info['percentage']:.1f}%"
        ])
        grades_data.append(total_row)

    # إنشاء جدول الدرجات
    grades_table = Table(grades_data)
    grades_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
        ('ALIGNMENT', (0, 0), (-1, -1), 'CENTER'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ('BACKGROUND', (0, 1), (-1, 1), colors.lightgrey),
        ('SPAN', (0, 0), (0, 1)),  # دمج خلايا "المادة"
        ('SPAN', (-2, 0), (-2, 1)),  # دمج خلايا "المجموع"
        ('SPAN', (-1, 0), (-1, 1))  # دمج خلايا "النتيجة"
    ]))

    elements.append(grades_table)

    # إنشاء الملف
    doc.build(elements)

    return True

def create_class_report(class_id, output_path):
    """إنشاء تقرير درجات للصف"""
    class_obj = Class.query.get(class_id)
    if not class_obj:
        return False

    # إنشاء ملف PDF
    doc = SimpleDocTemplate(
        output_path,
        pagesize=A4,
        rightMargin=72,
        leftMargin=72,
        topMargin=72,
        bottomMargin=72
    )

    # إنشاء نمط للنص العربي
    styles = getSampleStyleSheet()
    arabic_style = ParagraphStyle(
        'Arabic',
        parent=styles['Normal'],
        fontName='Arabic',
        alignment=1,  # وسط
        leading=14
    )

    arabic_bold_style = ParagraphStyle(
        'ArabicBold',
        parent=styles['Heading1'],
        fontName='ArabicBold',
        alignment=1,  # وسط
        leading=16
    )

    # إنشاء محتوى التقرير
    elements = []

    # عنوان التقرير
    title = Paragraph(f"تقرير درجات الصف: {class_obj.name}", arabic_bold_style)
    elements.append(title)
    elements.append(Paragraph("<br/>", arabic_style))

    # الحصول على الطلاب
    students = Student.query.filter_by(class_id=class_id).all()

    # إنشاء جدول الطلاب
    students_data = [["الرقم", "اسم الطالب", "الرقم الوطني", "المجموع", "النسبة المئوية", "النتيجة"]]

    for i, student in enumerate(students, 1):
        total_info = calculate_total_grade(student.id, class_id)
        if not total_info:
            continue

        is_promoted = "ناجح" if check_promotion_eligibility(student.id) else "راسب"

        students_data.append([
            str(i),
            student.full_name,
            student.national_id,
            f"{total_info['total_marks']:.1f}",
            f"{total_info['percentage']:.1f}%",
            is_promoted
        ])

    # إنشاء جدول الطلاب
    students_table = Table(students_data)
    students_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
        ('ALIGNMENT', (0, 0), (-1, -1), 'CENTER'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey)
    ]))

    elements.append(students_table)

    # إنشاء الملف
    doc.build(elements)

    return True

def create_exam_seating_cards(class_id, output_path):
    """إنشاء بطاقات جلوس الامتحان"""
    class_obj = Class.query.get(class_id)
    if not class_obj:
        return False

    # الحصول على الطلاب
    students = Student.query.filter_by(class_id=class_id).all()

    # إنشاء ملف PDF
    c = canvas.Canvas(output_path, pagesize=A4)

    # تحديد أبعاد البطاقة
    card_width = 8 * cm
    card_height = 5 * cm

    # عدد البطاقات في الصفحة
    cards_per_row = 2
    cards_per_column = 5

    # هوامش الصفحة
    margin_x = (A4[0] - (cards_per_row * card_width)) / 2
    margin_y = (A4[1] - (cards_per_column * card_height)) / 2

    # إنشاء البطاقات
    for i, student in enumerate(students):
        # حساب موقع البطاقة في الصفحة
        page_index = i // (cards_per_row * cards_per_column)
        card_index = i % (cards_per_row * cards_per_column)
        row = card_index // cards_per_row
        col = card_index % cards_per_row

        # إنشاء صفحة جديدة إذا لزم الأمر
        if card_index == 0 and i > 0:
            c.showPage()

        # حساب إحداثيات البطاقة
        x = margin_x + (col * card_width)
        y = A4[1] - margin_y - ((row + 1) * card_height)

        # رسم إطار البطاقة
        c.rect(x, y, card_width, card_height)

        # إضافة بيانات الطالب
        c.setFont("Arabic", 10)
        c.drawRightString(x + card_width - 10, y + card_height - 15, "بطاقة جلوس")
        c.drawRightString(x + card_width - 10, y + card_height - 30, f"الاسم: {student.full_name}")
        c.drawRightString(x + card_width - 10, y + card_height - 45, f"الرقم الوطني: {student.national_id}")
        c.drawRightString(x + card_width - 10, y + card_height - 60, f"رقم الجلوس: {student.seat_number or 'غير محدد'}")
        c.drawRightString(x + card_width - 10, y + card_height - 75, f"الصف: {class_obj.name}")
        c.drawRightString(x + card_width - 10, y + card_height - 90, f"الرقم السري: {student.secret_number}")

    # حفظ الملف
    c.save()

    return True
