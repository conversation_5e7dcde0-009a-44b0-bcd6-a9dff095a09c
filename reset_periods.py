from flask import Flask
from models import db, Period
from run import app

def reset_periods():
    """إعادة تعيين الفترات الدراسية الثابتة وحذف البيانات المتكررة"""
    
    print("بدء إعادة تعيين الفترات الدراسية...")
    
    # حذف جميع الفترات الدراسية الحالية
    print("حذف جميع الفترات الدراسية الحالية...")
    Period.query.delete()
    db.session.commit()
    
    # إنشاء الفترات الدراسية الثابتة
    print("إنشاء الفترات الدراسية الثابتة...")
    
    # تحديد العام الدراسي الحالي
    current_academic_year = "2023-2024"
    
    # إنشاء الفترات الدراسية الثابتة
    periods = [
        Period(name="الفصل الأول", academic_year=current_academic_year, is_approved=False),
        Period(name="الفصل الثاني", academic_year=current_academic_year, is_approved=False),
        Period(name="الفترة الأولى", academic_year=current_academic_year, is_approved=False),
        Period(name="الفترة الثانية", academic_year=current_academic_year, is_approved=False),
        Period(name="الدور الأول", academic_year=current_academic_year, is_approved=False),
        Period(name="الدور الثاني", academic_year=current_academic_year, is_approved=False)
    ]
    
    # إضافة الفترات الدراسية إلى قاعدة البيانات
    db.session.add_all(periods)
    db.session.commit()
    
    # التحقق من الفترات الدراسية بعد إعادة التعيين
    print("\nالفترات الدراسية بعد إعادة التعيين:")
    all_periods = Period.query.all()
    for period in all_periods:
        print(f"- {period.id}: {period.name} ({period.academic_year}), معتمدة: {'نعم' if period.is_approved else 'لا'}")
    
    print("\nتم إعادة تعيين الفترات الدراسية بنجاح.")

if __name__ == "__main__":
    with app.app_context():
        reset_periods()
