import sys
sys.path.append('.')

try:
    print("Starting test...")
    from app import app
    print("App imported successfully")
    
    from models import db, Student, Subject, Period, StudentGrade, Class
    print("Models imported successfully")
    
    with app.app_context():
        print("App context created")
        
        # اختبار بسيط
        students = Student.query.all()
        print(f"Found {len(students)} students")
        
        subjects = Subject.query.all()
        print(f"Found {len(subjects)} subjects")
        
        periods = Period.query.all()
        print(f"Found {len(periods)} periods")
        
        # كتابة النتائج في ملف
        with open('test_results.txt', 'w', encoding='utf-8') as f:
            f.write(f"Students: {len(students)}\n")
            f.write(f"Subjects: {len(subjects)}\n")
            f.write(f"Periods: {len(periods)}\n")
            
            f.write("\nPeriods list:\n")
            for period in periods:
                f.write(f"- {period.id}: {period.name}\n")
        
        print("Test completed successfully!")
        
except Exception as e:
    print(f"Error: {e}")
    with open('error_log.txt', 'w', encoding='utf-8') as f:
        import traceback
        f.write(str(e) + '\n')
        f.write(traceback.format_exc())