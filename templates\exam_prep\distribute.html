{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">توزيع الطلاب على القاعات</h1>
        <a href="{{ url_for('exam_prep') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-arrow-right ml-1"></i> العودة
        </a>
    </div>
    
    <!-- نموذج توزيع الطلاب -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 class="text-lg font-semibold mb-4">إعدادات توزيع الطلاب</h2>
        
        <form id="distribute-form">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="grade-select" class="block text-gray-700 font-medium mb-2">الصف الدراسي</label>
                    <select id="grade-select" name="grade_id" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="" disabled selected>اختر الصف الدراسي</option>
                        {% for grade in grades %}
                        <option value="{{ grade.id }}">{{ grade.name }} - {{ grade.track }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="hall-count" class="block text-gray-700 font-medium mb-2">عدد القاعات</label>
                    <input type="number" id="hall-count" name="hall_count" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" value="2" min="1">
                </div>
                
                <div>
                    <label for="distribution-type" class="block text-gray-700 font-medium mb-2">نوع التوزيع</label>
                    <select id="distribution-type" name="distribution_type" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="sequential" selected>متسلسل (1-10 في القاعة الأولى، 11-20 في الثانية، ...)</option>
                        <option value="alternate">متبادل (الفردي في القاعة الأولى، الزوجي في الثانية)</option>
                    </select>
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="button" id="generate-btn" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors">
                    <i class="fas fa-cog ml-1"></i> توزيع الطلاب
                </button>
            </div>
        </form>
    </div>
    
    <!-- نتائج التوزيع -->
    <div id="results-container" class="hidden">
        <h2 class="text-xl font-semibold mb-4">نتائج توزيع الطلاب</h2>
        
        <div id="halls-container" class="space-y-6">
            <!-- سيتم ملء هذا القسم بواسطة JavaScript -->
        </div>
        
        <div class="mt-6 flex justify-end">
            <button id="print-btn" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-6 rounded transition-colors">
                <i class="fas fa-print ml-1"></i> طباعة التوزيع
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // توزيع الطلاب على القاعات
    document.getElementById('generate-btn').addEventListener('click', function() {
        const gradeId = document.getElementById('grade-select').value;
        const hallCount = parseInt(document.getElementById('hall-count').value);
        const distributionType = document.getElementById('distribution-type').value;
        
        if (!gradeId || !hallCount) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }
        
        // طلب بيانات الطلاب من الخادم
        fetch(`/api/students?grade_id=${gradeId}`)
            .then(response => response.json())
            .then(data => {
                if (!data.students || data.students.length === 0) {
                    alert('لا يوجد طلاب مسجلين في هذا الصف الدراسي');
                    return;
                }
                
                // توزيع الطلاب على القاعات
                const halls = distributeStudents(data.students, hallCount, distributionType);
                
                // عرض نتائج التوزيع
                displayResults(halls);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء تحميل البيانات');
            });
    });
    
    // توزيع الطلاب على القاعات
    function distributeStudents(students, hallCount, distributionType) {
        const halls = Array.from({ length: hallCount }, () => []);
        
        if (distributionType === 'sequential') {
            // التوزيع المتسلسل
            const studentsPerHall = Math.ceil(students.length / hallCount);
            
            for (let i = 0; i < students.length; i++) {
                const hallIndex = Math.floor(i / studentsPerHall);
                if (hallIndex < hallCount) {
                    halls[hallIndex].push(students[i]);
                } else {
                    halls[hallCount - 1].push(students[i]);
                }
            }
        } else if (distributionType === 'alternate') {
            // التوزيع المتبادل
            for (let i = 0; i < students.length; i++) {
                const hallIndex = i % hallCount;
                halls[hallIndex].push(students[i]);
            }
        }
        
        return halls;
    }
    
    // عرض نتائج التوزيع
    function displayResults(halls) {
        const hallsContainer = document.getElementById('halls-container');
        hallsContainer.innerHTML = '';
        
        halls.forEach((students, index) => {
            const hallDiv = document.createElement('div');
            hallDiv.className = 'bg-gray-50 p-4 rounded-lg';
            
            hallDiv.innerHTML = `
                <h3 class="text-lg font-semibold mb-3">القاعة ${index + 1} (${students.length} طالب)</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-200">
                        <thead>
                            <tr class="bg-gray-100">
                                <th class="py-2 px-4 border-b text-right">#</th>
                                <th class="py-2 px-4 border-b text-right">الاسم</th>
                                <th class="py-2 px-4 border-b text-right">الرقم الوطني</th>
                                <th class="py-2 px-4 border-b text-right">الفصل</th>
                                <th class="py-2 px-4 border-b text-right">رقم الجلوس</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${students.map((student, i) => `
                                <tr class="hover:bg-gray-50">
                                    <td class="py-2 px-4 border-b">${i + 1}</td>
                                    <td class="py-2 px-4 border-b">${student.name}</td>
                                    <td class="py-2 px-4 border-b">${student.national_id}</td>
                                    <td class="py-2 px-4 border-b">${student.class_name}</td>
                                    <td class="py-2 px-4 border-b">${student.seat_number}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            hallsContainer.appendChild(hallDiv);
        });
        
        document.getElementById('results-container').classList.remove('hidden');
    }
    
    // طباعة التوزيع
    document.getElementById('print-btn').addEventListener('click', function() {
        window.print();
    });
</script>
{% endblock %}
