from database.models import Grade, Subject, StageSubject, ClassSubject, AcademicPeriod

def calculate_subject_grade(student_id, class_subject_id, period_id):
    """حساب درجة الطالب في مادة معينة لفترة معينة"""
    grade = Grade.query.filter_by(
        student_id=student_id,
        class_subject_id=class_subject_id,
        period_id=period_id
    ).first()

    if not grade:
        return {
            'classwork': None,
            'exam': None,
            'total': None,
            'is_passed': False,
            'grade_id': None
        }

    # استخدام درجة امتحان الدور الثاني إذا كانت موجودة
    exam_mark = grade.second_chance_exam_mark if grade.second_chance_exam_mark is not None else grade.exam_mark

    # استخدام الدرجة الكلية المخزنة إذا كانت موجودة، وإلا حسابها
    total = grade.total_mark if grade.total_mark is not None else (grade.classwork_mark + exam_mark)

    return {
        'classwork': grade.classwork_mark,
        'exam': exam_mark,
        'total': total,
        'is_passed': grade.is_passed,
        'grade_id': grade.id
    }

def calculate_total_grade(student_id, class_id):
    """حساب المجموع الكلي للطالب في جميع المواد"""
    from database.models import Student

    student = Student.query.get(student_id)
    if not student or student.class_id != class_id:
        return {
            'total': None,
            'total_possible': 0,
            'percentage': 0
        }

    # الحصول على المواد المرتبطة بصف الطالب
    class_subjects = ClassSubject.query.filter_by(class_id=class_id, is_active=True).all()

    # الحصول على جميع الفترات للصف
    periods = AcademicPeriod.query.filter_by(class_id=class_id).all()

    if not periods or not class_subjects:
        return {
            'total': 0,
            'total_possible': 0,
            'percentage': 0
        }

    total_marks = 0
    total_possible_marks = 0

    for class_subject in class_subjects:
        subject_total = 0
        period_count = 0

        for period in periods:
            grade_info = calculate_subject_grade(student_id, class_subject.id, period.id)
            if grade_info['total'] is not None:
                subject_total += grade_info['total']
                period_count += 1

        # حساب متوسط درجة المادة عبر جميع الفترات
        if period_count > 0:
            subject_average = subject_total / period_count
            total_marks += subject_average
            total_possible_marks += class_subject.total_mark

    return {
        'total': total_marks,
        'total_possible': total_possible_marks,
        'percentage': (total_marks / total_possible_marks) * 100 if total_possible_marks > 0 else 0
    }

def check_promotion_eligibility(student_id):
    """التحقق من أهلية الطالب للترقية للصف التالي"""
    from database.models import Student, Grade

    student = Student.query.get(student_id)
    if not student:
        return False

    # الحصول على المواد المرتبطة بصف الطالب
    class_subjects = ClassSubject.query.filter_by(class_id=student.class_id, is_active=True).all()

    # الحصول على جميع الفترات للصف
    periods = AcademicPeriod.query.filter_by(class_id=student.class_id).all()

    if not class_subjects or not periods:
        return False

    # التحقق من نجاح الطالب في جميع المواد
    for class_subject in class_subjects:
        subject_passed = False

        # التحقق من درجات الطالب في هذه المادة عبر جميع الفترات
        for period in periods:
            grade = Grade.query.filter_by(
                student_id=student_id,
                class_subject_id=class_subject.id,
                period_id=period.id
            ).first()

            if grade and grade.is_passed:
                subject_passed = True
                break

        # إذا لم ينجح الطالب في هذه المادة في أي فترة، فهو غير مؤهل للترقية
        if not subject_passed:
            return False

    # الطالب مؤهل للترقية إذا نجح في جميع المواد
    return True

def calculate_class_statistics(class_id):
    """حساب إحصائيات الصف (نسبة النجاح، متوسط الدرجات، إلخ)"""
    from database.models import Student

    students = Student.query.filter_by(class_id=class_id).all()

    total_students = len(students)
    if total_students == 0:
        return {
            'total_students': 0,
            'passed_students': 0,
            'failed_students': 0,
            'pass_rate': 0,
            'average_grade': 0
        }

    passed_students = sum(1 for student in students if check_promotion_eligibility(student.id))

    # حساب متوسط الدرجات
    total_grades = 0
    valid_students = 0
    for student in students:
        grade_info = calculate_total_grade(student.id, class_id)
        if grade_info and grade_info['total'] is not None:
            total_grades += grade_info['percentage']
            valid_students += 1

    average_grade = total_grades / valid_students if valid_students > 0 else 0

    return {
        'total_students': total_students,
        'passed_students': passed_students,
        'failed_students': total_students - passed_students,
        'pass_rate': (passed_students / total_students) * 100 if total_students > 0 else 0,
        'average_grade': average_grade
    }
