# نظام إدارة درجات الطلبة

تطبيق سطح مكتب بواجهة ويب لإدارة درجات طلبة التعليم الثانوي في ليبيا.

## المميزات

- إدارة بيانات الطلاب
- إدارة الفصول الدراسية والمواد
- رصد الدرجات للفصول الدراسية المختلفة
- إدارة امتحانات الدور الثاني للطلاب المتعثرين
- إصدار التقارير وتوزيع الطلاب على القاعات

## التقنيات المستخدمة

- **الخلفية (Backend)**: Flask (Python)
- **الواجهة (Frontend)**: HTML + Tailwind CSS
- **قاعدة البيانات**: SQLite
- **تطبيق سطح المكتب**: PyWebview

## متطلبات التشغيل

- Python 3.8 أو أحدث
- المكتبات المذكورة في ملف `requirements.txt`

## طريقة التثبيت

1. قم بتثبيت Python من [الموقع الرسمي](https://www.python.org/downloads/)
2. قم بتنزيل أو استنساخ هذا المشروع
3. افتح موجه الأوامر (Command Prompt) في مجلد المشروع
4. قم بتثبيت المكتبات المطلوبة:

```
pip install -r requirements.txt
```

## طريقة التشغيل

لتشغيل التطبيق كتطبيق سطح مكتب:

```
python run.py
```

لتشغيل التطبيق في وضع التصحيح (كتطبيق ويب):

```
python run.py --debug
```

## هيكل المشروع

- `app.py`: نقطة الدخول الرئيسية للتطبيق
- `app_config.py`: إعدادات التطبيق
- `models.py`: نماذج قاعدة البيانات
- `routes.py`: مسارات التطبيق
- `run.py`: ملف تشغيل التطبيق
- `templates/`: قوالب HTML
- `static/`: الملفات الثابتة (CSS، JavaScript، الصور)

## الترخيص

جميع الحقوق محفوظة © 2023
