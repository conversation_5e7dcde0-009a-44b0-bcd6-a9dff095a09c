{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">إضافة فصل جديد</h1>
        <a href="{{ url_for('list_classes') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-arrow-right ml-1"></i> العودة
        </a>
    </div>
    
    <form method="POST" action="{{ url_for('add_class') }}">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
                <label for="name" class="block text-gray-700 font-medium mb-2">اسم الفصل</label>
                <input type="text" id="name" name="name" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
            </div>
            
            <div>
                <label for="grade_id" class="block text-gray-700 font-medium mb-2">الصف الدراسي</label>
                <select id="grade_id" name="grade_id" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                    <option value="" disabled selected>اختر الصف الدراسي</option>
                    {% for grade in grades %}
                    <option value="{{ grade.id }}">{{ grade.name }} - {{ grade.track }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div>
                <label for="academic_year" class="block text-gray-700 font-medium mb-2">العام الدراسي</label>
                <input type="text" id="academic_year" name="academic_year" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="مثال: 2023-2024" required>
            </div>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors">
                <i class="fas fa-save ml-1"></i> حفظ
            </button>
        </div>
    </form>
</div>
{% endblock %}
