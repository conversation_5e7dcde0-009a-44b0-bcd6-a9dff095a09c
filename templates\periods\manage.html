{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <h1 class="text-2xl font-bold mb-6 text-blue-600">إدارة الفترات الدراسية</h1>

    <!-- تحديث العام الدراسي -->
    <div class="mb-6 bg-blue-50 p-4 rounded-lg">
        <h2 class="text-lg font-semibold mb-4">تحديث العام الدراسي</h2>
        <form method="POST" action="{{ url_for('update_academic_year') }}">
            <div class="flex items-center">
                <div class="flex-grow">
                    <label for="academic_year" class="block text-gray-700 font-medium mb-2">العام الدراسي الجديد <span class="text-red-500">*</span></label>
                    <input type="text" id="academic_year" name="academic_year" required class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="مثال: 2024-2025">
                </div>
                <div class="mr-4 mt-6">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors">
                        <i class="fas fa-sync-alt ml-1"></i> تحديث
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- جدول الفترات الدراسية -->
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
            <thead>
                <tr class="bg-gray-100">
                    <th class="py-3 px-4 border-b text-right">#</th>
                    <th class="py-3 px-4 border-b text-right">اسم الفترة</th>
                    <th class="py-3 px-4 border-b text-right">العام الدراسي</th>
                    <th class="py-3 px-4 border-b text-center">حالة الاعتماد</th>
                    <th class="py-3 px-4 border-b text-center">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for period in periods %}
                <tr class="hover:bg-gray-50">
                    <td class="py-3 px-4 border-b">{{ loop.index }}</td>
                    <td class="py-3 px-4 border-b">{{ period.name }}</td>
                    <td class="py-3 px-4 border-b">{{ period.academic_year }}</td>
                    <td class="py-3 px-4 border-b text-center">
                        {% if period.is_approved %}
                        <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">معتمد</span>
                        {% else %}
                        <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">غير معتمد</span>
                        {% endif %}
                    </td>
                    <td class="py-3 px-4 border-b text-center">
                        {% if period.is_approved %}
                        <form method="POST" action="{{ url_for('unapprove_period', id=period.id) }}" class="inline">
                            <button type="submit" class="bg-yellow-500 hover:bg-yellow-600 text-white text-sm py-1 px-3 rounded transition-colors">
                                <i class="fas fa-unlock ml-1"></i> فك الاعتماد
                            </button>
                        </form>
                        {% else %}
                        <form method="POST" action="{{ url_for('approve_period', id=period.id) }}" class="inline">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white text-sm py-1 px-3 rounded transition-colors">
                                <i class="fas fa-lock ml-1"></i> اعتماد
                            </button>
                        </form>
                        {% endif %}
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="5" class="py-4 text-center text-gray-500">لا توجد فترات دراسية مسجلة</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- ملاحظة: تم إزالة قسم إضافة الفترات الدراسية لأن الفترات الدراسية ثابتة في النظام -->
</div>
{% endblock %}
