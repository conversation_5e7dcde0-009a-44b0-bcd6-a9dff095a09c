{% extends 'base.html' %}

{% block title %}درجات الصف {{ class_obj.name }} - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}درجات الصف: {{ class_obj.name }} ({{ class_obj.stage.name }}){% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <span>إحصائيات الصف</span>
                <a href="{{ url_for('grades.index') }}" class="btn btn-sm btn-light">
                    <i class="fas fa-arrow-right me-1"></i>
                    رجوع
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title">عدد الطلاب</h5>
                                <h2 class="display-4">{{ class_stats.total_students }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">الناجحون</h5>
                                <h2 class="display-4">{{ class_stats.passed_students }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">الراسبون</h5>
                                <h2 class="display-4">{{ class_stats.failed_students }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">نسبة النجاح</h5>
                                <h2 class="display-4">{{ class_stats.pass_rate|round(1) }}%</h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    قائمة طلاب الصف
                </h5>
            </div>
            <div class="card-body">
                {% if students %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم الطالب</th>
                                <th>الرقم الوطني</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in students %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ student.full_name }}</td>
                                <td>{{ student.national_id }}</td>
                                <td>
                                    {% if check_promotion_eligibility(student.id) %}
                                    <span class="badge bg-success">ناجح</span>
                                    {% else %}
                                    <span class="badge bg-danger">راسب</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('grades.student_grades', student_id=student.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-chart-bar me-1"></i>
                                        عرض الدرجات
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لا يوجد طلاب مسجلين في هذا الصف حالياً.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    جدول الدرجات
                </h5>
            </div>
            <div class="card-body">
                {% if students and subjects and periods %}
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th rowspan="2">الطالب</th>
                                {% for subject in subjects %}
                                <th colspan="{{ periods|length }}">{{ subject.name }}</th>
                                {% endfor %}
                                <th rowspan="2">المجموع</th>
                                <th rowspan="2">النتيجة</th>
                            </tr>
                            <tr>
                                {% for subject in subjects %}
                                {% for period in periods %}
                                <th>{{ period.name }}</th>
                                {% endfor %}
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in students %}
                            <tr>
                                <td>{{ student.full_name }}</td>

                                {% for subject in subjects %}
                                {% for period in periods %}
                                <td>
                                    {% set grade_info = calculate_subject_grade(student.id, subject.id, period.id) %}
                                    {% if grade_info.total is not none %}
                                    <a href="{{ url_for('grades.add_student_grade', student_id=student.id, subject_id=subject.id, period_id=period.id) }}" class="text-decoration-none">
                                        {{ grade_info.total|round(1) }}
                                        {% if not grade_info.is_passed %}
                                        <span class="text-danger">*</span>
                                        {% endif %}
                                    </a>
                                    {% else %}
                                    <a href="{{ url_for('grades.add_student_grade', student_id=student.id, subject_id=subject.id, period_id=period.id) }}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                    {% endif %}
                                </td>
                                {% endfor %}
                                {% endfor %}

                                <td class="fw-bold">
                                    {% set total_info = calculate_total_grade(student.id, class_obj.id) %}
                                    {% if total_info and total_info.total %}
                                    {{ "%.1f"|format(total_info.total) }}
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if check_promotion_eligibility(student.id) %}
                                    <span class="badge bg-success">ناجح</span>
                                    {% else %}
                                    <span class="badge bg-danger">راسب</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد بيانات كافية لعرض جدول الدرجات.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="mt-4">
    <a href="{{ url_for('grades.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>
        رجوع
    </a>
    <a href="{{ url_for('reports.class_report', class_id=class_obj.id) }}" class="btn btn-primary">
        <i class="fas fa-file-pdf me-1"></i>
        إنشاء تقرير PDF
    </a>
    <a href="{{ url_for('reports.class_excel', class_id=class_obj.id) }}" class="btn btn-success">
        <i class="fas fa-file-excel me-1"></i>
        تصدير إلى Excel
    </a>
</div>
{% endblock %}
