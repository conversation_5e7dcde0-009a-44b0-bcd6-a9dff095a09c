import sys
sys.path.append('.')

from app import app
from models import db, Student, Subject, Period, StudentGrade, Class
from flask import request
import json

with app.app_context():
    try:
        print("Testing direct function call...")
        
        # محاكاة الطلب
        with app.test_request_context('/grades/combine-semesters/all-data?class_id=1'):
            # استيراد الدالة من app_routes
            from app_routes import get_all_combined_data
            
            # استدعاء الدالة مباشرة
            result = get_all_combined_data()
            
            # التحقق من النتيجة
            if hasattr(result, 'get_json'):
                data = result.get_json()
                print(f"Status Code: {result.status_code}")
                print(f"Data keys: {list(data.keys()) if data else 'No data'}")
                
                if 'students' in data:
                    print(f"Number of students: {len(data['students'])}")
                if 'subjects' in data:
                    print(f"Number of subjects: {len(data['subjects'])}")
                    
                # كتابة النتيجة في ملف
                with open('api_result.json', 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                    
                print("Result saved to api_result.json")
            else:
                print(f"Unexpected result type: {type(result)}")
                print(f"Result: {result}")
                
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        
        with open('direct_test_error.txt', 'w', encoding='utf-8') as f:
            f.write(str(e) + '\n')
            f.write(traceback.format_exc())