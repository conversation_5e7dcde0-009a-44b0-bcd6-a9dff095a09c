{% extends 'base.html' %}

{% block title %}رصد الدرجات - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}رصد الدرجات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search me-2"></i>
                    البحث عن طالب
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">يمكنك البحث عن طالب لرصد درجاته باستخدام الاسم أو الرقم الوطني أو الرقم السري.</p>
                <a href="{{ url_for('grades.search') }}" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>
                    البحث عن طالب
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    عرض درجات الصف
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">يمكنك عرض درجات جميع طلاب صف معين وإدارتها بشكل جماعي.</p>
                <form action="#" method="get" id="classGradesForm" data-url-template="{{ url_for('view_class_grades', class_id='CLASS_ID_PLACEHOLDER') }}">
                    <div class="mb-3">
                        <label for="class_id" class="form-label">اختر الصف</label>
                        <select class="form-select" id="class_id" name="class_id" required>
                            <option value="">-- اختر الصف --</option>
                            {% for class_obj in classes %}
                            <option value="{{ class_obj.id }}">{{ class_obj.name }} ({{ class_obj.stage.name }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <button type="button" class="btn btn-success" onclick="redirectToClassGrades()">
                        <i class="fas fa-eye me-1"></i>
                        عرض درجات الصف
                    </button>
                </form>

                <script>
                    function redirectToClassGrades() {
                        var classId = document.getElementById('class_id').value;
                        if (classId) {
                            const form = document.getElementById('classGradesForm');
                            const urlTemplate = form.dataset.urlTemplate;
                            window.location.href = urlTemplate.replace('CLASS_ID_PLACEHOLDER', classId);
                        } else {
                            alert('الرجاء اختيار الصف أولاً');
                        }
                    }
                </script>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-redo me-2"></i>
                    الدور الثاني
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">إدارة امتحانات الدور الثاني للطلاب المتعثرين ورصد درجاتهم.</p>
                <a href="{{ url_for('grades.second_chance') }}" class="btn btn-warning">
                    <i class="fas fa-redo me-1"></i>
                    الذهاب إلى الدور الثاني
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    التقارير
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">إصدار تقارير متنوعة مثل كشوف الدرجات وتوزيع الطلاب على القاعات.</p>
                <a href="{{ url_for('reports.index') }}" class="btn btn-info">
                    <i class="fas fa-file-alt me-1"></i>
                    الذهاب إلى التقارير
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
