{% extends 'base.html' %}

{% block title %}البحث عن طالب - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}البحث عن طالب لرصد الدرجات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-body">
                <form action="{{ url_for('grades.search') }}" method="post">
                    <div class="mb-3">
                        <label for="search_type" class="form-label">طريقة البحث</label>
                        <select class="form-select" id="search_type" name="search_type" required>
                            <option value="name">البحث بالاسم</option>
                            <option value="national_id">البحث بالرقم الوطني</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="search_value" class="form-label">قيمة البحث</label>
                        <input type="text" class="form-control" id="search_value" name="search_value" required>
                        <div class="form-text" id="search_help">أدخل اسم الطالب أو جزء منه.</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('grades.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            رجوع
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>
                            بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener("DOMContentLoaded", function() {
        var searchType = document.getElementById("search_type");
        var searchHelp = document.getElementById("search_help");

        function updateHelp() {
            switch (searchType.value) {
                case "name":
                    searchHelp.textContent = "أدخل اسم الطالب أو جزء منه.";
                    break;
                case "national_id":
                    searchHelp.textContent = "أدخل الرقم الوطني المكون من 12 رقم.";
                    break;
            }
        }

        searchType.addEventListener("change", updateHelp);
        updateHelp();
    });
</script>
{% endblock %}
