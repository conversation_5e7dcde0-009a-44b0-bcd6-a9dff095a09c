from .models import db, Student, EducationalStage, Class, AcademicPeriod, Subject, StageSubject, ClassSubject, Grade, ExamHall

def init_db(app, recreate=False):
    """تهيئة قاعدة البيانات"""
    db.init_app(app)
    with app.app_context():
        if recreate:
            # إعادة إنشاء قاعدة البيانات
            db.drop_all()
            db.create_all()
            # إنشاء البيانات الأولية
            create_initial_data()
        else:
            # إنشاء الجداول إذا لم تكن موجودة
            db.create_all()
            # إنشاء البيانات الأولية إذا كانت قاعدة البيانات فارغة
            create_initial_data()

def create_initial_data():
    """إنشاء البيانات الأولية في قاعدة البيانات"""
    # التحقق من وجود بيانات
    if EducationalStage.query.count() == 0:
        # إنشاء المراحل الدراسية
        primary = EducationalStage(name="أساسي")
        preparatory = EducationalStage(name="إعدادي")
        secondary = EducationalStage(name="ثانوي")

        db.session.add_all([primary, preparatory, secondary])
        db.session.commit()

        # إنشاء الصفوف الدراسية
        classes = [
            # المرحلة الأساسية
            Class(name="الصف الأول", stage=primary, order=1, evaluation_type='period'),
            Class(name="الصف الثاني", stage=primary, order=2, evaluation_type='period'),
            Class(name="الصف الثالث", stage=primary, order=3, evaluation_type='period'),
            Class(name="الصف الرابع", stage=primary, order=4, evaluation_type='semester'),
            Class(name="الصف الخامس", stage=primary, order=5, evaluation_type='semester'),
            Class(name="الصف السادس", stage=primary, order=6, evaluation_type='semester'),
            # المرحلة الإعدادية
            Class(name="الصف السابع", stage=preparatory, order=7, evaluation_type='semester'),
            Class(name="الصف الثامن", stage=preparatory, order=8, evaluation_type='semester'),
            Class(name="الصف التاسع", stage=preparatory, order=9, evaluation_type='semester', has_unified_exam=True),
            # المرحلة الثانوية
            Class(name="الصف الأول الثانوي", stage=secondary, order=10, specialization="عام", evaluation_type='semester'),
            Class(name="الصف الثاني الثانوي", stage=secondary, order=11, specialization="علمي", evaluation_type='semester'),
            Class(name="الصف الثاني الثانوي", stage=secondary, order=11, specialization="أدبي", evaluation_type='semester'),
            Class(name="الصف الثالث الثانوي", stage=secondary, order=12, specialization="علمي", evaluation_type='period', has_unified_exam=True),
            Class(name="الصف الثالث الثانوي", stage=secondary, order=12, specialization="أدبي", evaluation_type='period', has_unified_exam=True),
        ]

        db.session.add_all(classes)
        db.session.commit()

        # إنشاء الفترات الدراسية
        for class_obj in classes:
            if class_obj.evaluation_type == 'period':
                if class_obj.stage_id == primary.id and class_obj.name in ["الصف الأول", "الصف الثاني", "الصف الثالث"]:
                    # الصفوف الدنيا لها ثلاث فترات
                    periods = [
                        AcademicPeriod(name="الفترة الأولى", class_id=class_obj.id, period_type='period', weight=0.3),
                        AcademicPeriod(name="الفترة الثانية", class_id=class_obj.id, period_type='period', weight=0.3),
                        AcademicPeriod(name="الفترة الثالثة", class_id=class_obj.id, period_type='period', weight=0.4),
                    ]
                elif class_obj.has_unified_exam and "الثالث الثانوي" in class_obj.name:
                    # الصف الثالث الثانوي له فترتين + امتحان نهائي موحد
                    periods = [
                        AcademicPeriod(name="الفترة الأولى", class_id=class_obj.id, period_type='period', weight=0.2),
                        AcademicPeriod(name="الفترة الثانية", class_id=class_obj.id, period_type='period', weight=0.2),
                        AcademicPeriod(name="الامتحان النهائي", class_id=class_obj.id, period_type='final', weight=0.6, is_final=True),
                    ]
                else:
                    # فترات أخرى
                    periods = [
                        AcademicPeriod(name="الفترة الأولى", class_id=class_obj.id, period_type='period', weight=0.5),
                        AcademicPeriod(name="الفترة الثانية", class_id=class_obj.id, period_type='period', weight=0.5),
                    ]
            else:
                # نظام الفصلين الدراسيين
                if class_obj.has_unified_exam and class_obj.name == "الصف التاسع":
                    # الصف التاسع له فصلين + امتحان نهائي موحد
                    periods = [
                        AcademicPeriod(name="الفصل الأول", class_id=class_obj.id, period_type='semester', weight=0.3),
                        AcademicPeriod(name="الفصل الثاني", class_id=class_obj.id, period_type='semester', weight=0.3),
                        AcademicPeriod(name="الامتحان النهائي", class_id=class_obj.id, period_type='final', weight=0.4, is_final=True),
                    ]
                else:
                    # باقي الصفوف لها فصلين دراسيين
                    periods = [
                        AcademicPeriod(name="الفصل الأول", class_id=class_obj.id, period_type='semester', weight=0.5),
                        AcademicPeriod(name="الفصل الثاني", class_id=class_obj.id, period_type='semester', weight=0.5),
                    ]

            db.session.add_all(periods)

        db.session.commit()

        # إنشاء المواد الدراسية الأساسية
        subjects = [
            Subject(name="اللغة العربية", code="AR", description="مادة اللغة العربية الأساسية"),
            Subject(name="اللغة الإنجليزية", code="EN", description="مادة اللغة الإنجليزية"),
            Subject(name="الرياضيات", code="MATH", description="مادة الرياضيات"),
            Subject(name="العلوم", code="SCI", description="مادة العلوم العامة"),
            Subject(name="الدراسات الاجتماعية", code="SOC", description="مادة الدراسات الاجتماعية"),
            Subject(name="التربية الإسلامية", code="ISL", description="مادة التربية الإسلامية"),
            Subject(name="الحاسوب", code="COMP", description="مادة الحاسوب وتكنولوجيا المعلومات"),
            Subject(name="التربية الفنية", code="ART", description="مادة التربية الفنية"),
            Subject(name="التربية البدنية", code="PE", description="مادة التربية البدنية والرياضية"),
        ]

        db.session.add_all(subjects)
        db.session.commit()

        # ربط المواد بالمراحل الدراسية مع تحديد الدرجات وطرق التقييم
        stage_subjects = []

        # المرحلة الأساسية (الصفوف 1-6)
        for subject in subjects:
            if subject.name in ["التربية الفنية", "التربية البدنية"] and subject.name in ["الصف الأول", "الصف الثاني", "الصف الثالث"]:
                # مواد التقييم اليومي للصفوف 1-3
                stage_subjects.append(
                    StageSubject(
                        subject_id=subject.id,
                        stage_id=primary.id,
                        total_mark=100,
                        classwork_percentage=100,
                        exam_percentage=0,
                        passing_percentage=50,
                        min_exam_percentage=0,
                        daily_evaluation_only=True
                    )
                )
            elif subject.name in ["اللغة العربية", "الرياضيات"]:
                # المواد الأساسية لها وزن أكبر
                stage_subjects.append(
                    StageSubject(
                        subject_id=subject.id,
                        stage_id=primary.id,
                        total_mark=100,
                        classwork_percentage=40,
                        exam_percentage=60,
                        passing_percentage=50,
                        min_exam_percentage=40
                    )
                )
            else:
                # باقي المواد
                stage_subjects.append(
                    StageSubject(
                        subject_id=subject.id,
                        stage_id=primary.id,
                        total_mark=100,
                        classwork_percentage=40,
                        exam_percentage=60,
                        passing_percentage=50,
                        min_exam_percentage=40
                    )
                )

        # المرحلة الإعدادية (الصفوف 7-9)
        for subject in subjects:
            stage_subjects.append(
                StageSubject(
                    subject_id=subject.id,
                    stage_id=preparatory.id,
                    total_mark=100,
                    classwork_percentage=30,
                    exam_percentage=70,
                    passing_percentage=50,
                    min_exam_percentage=40
                )
            )

        # المرحلة الثانوية (الصفوف 10-12)
        for subject in subjects:
            stage_subjects.append(
                StageSubject(
                    subject_id=subject.id,
                    stage_id=secondary.id,
                    total_mark=100,
                    classwork_percentage=20,
                    exam_percentage=80,
                    passing_percentage=50,
                    min_exam_percentage=40
                )
            )

        db.session.add_all(stage_subjects)
        db.session.commit()

        # ربط المواد بالصفوف الدراسية مع تحديد الدرجات وطرق التقييم
        class_subjects = []

        # الحصول على جميع الصفوف
        classes = Class.query.all()

        # لكل صف، إضافة المواد المرتبطة بمرحلته
        for class_obj in classes:
            # الحصول على المواد المرتبطة بمرحلة الصف
            stage_id = class_obj.stage_id
            stage_subjects_for_class = StageSubject.query.filter_by(stage_id=stage_id).all()

            for stage_subject in stage_subjects_for_class:
                # تعديل الدرجات وطرق التقييم حسب الصف
                total_mark = stage_subject.total_mark
                classwork_percentage = stage_subject.classwork_percentage
                exam_percentage = stage_subject.exam_percentage
                passing_percentage = stage_subject.passing_percentage
                min_exam_percentage = stage_subject.min_exam_percentage
                daily_evaluation_only = stage_subject.daily_evaluation_only

                # الحصول على المادة الأساسية
                subject = Subject.query.get(stage_subject.subject_id)

                # تعديل الإعدادات حسب الصف
                if class_obj.name == "الصف الأول":
                    # الصف الأول: تقييم أسهل
                    passing_percentage = 40.0
                    if subject.name in ["التربية الفنية", "التربية البدنية"]:
                        daily_evaluation_only = True
                elif class_obj.name == "الصف الثاني":
                    # الصف الثاني: تقييم أسهل
                    passing_percentage = 45.0
                    if subject.name in ["التربية الفنية", "التربية البدنية"]:
                        daily_evaluation_only = True
                elif class_obj.name == "الصف الثالث":
                    # الصف الثالث: تقييم أسهل
                    passing_percentage = 45.0
                    if subject.name in ["التربية الفنية", "التربية البدنية"]:
                        daily_evaluation_only = True
                elif class_obj.name in ["الصف الرابع", "الصف الخامس", "الصف السادس"]:
                    # الصفوف 4-6: تقييم متوسط
                    if subject.name in ["اللغة العربية", "الرياضيات"]:
                        # المواد الأساسية لها وزن أكبر
                        classwork_percentage = 30.0
                        exam_percentage = 70.0
                elif class_obj.name in ["الصف السابع", "الصف الثامن", "الصف التاسع"]:
                    # الصفوف 7-9: تقييم أصعب
                    if subject.name in ["اللغة العربية", "الرياضيات", "العلوم"]:
                        # المواد الأساسية لها وزن أكبر
                        classwork_percentage = 30.0
                        exam_percentage = 70.0
                        min_exam_percentage = 50.0
                elif class_obj.name in ["الصف العاشر", "الصف الحادي عشر", "الصف الثاني عشر"]:
                    # الصفوف 10-12: تقييم أصعب
                    classwork_percentage = 20.0
                    exam_percentage = 80.0
                    min_exam_percentage = 50.0

                # إنشاء ربط بين المادة والصف
                class_subject = ClassSubject(
                    class_id=class_obj.id,
                    stage_subject_id=stage_subject.id,
                    total_mark=total_mark,
                    classwork_percentage=classwork_percentage,
                    exam_percentage=exam_percentage,
                    passing_percentage=passing_percentage,
                    min_exam_percentage=min_exam_percentage,
                    daily_evaluation_only=daily_evaluation_only
                )

                class_subjects.append(class_subject)

        db.session.add_all(class_subjects)
        db.session.commit()

# وظائف التعامل مع الطلاب
def add_student(full_name, national_id, secret_number, seat_number, class_id):
    """إضافة طالب جديد"""
    student = Student(
        full_name=full_name,
        national_id=national_id,
        secret_number=secret_number,
        seat_number=seat_number,
        class_id=class_id
    )
    db.session.add(student)
    db.session.commit()
    return student

def get_student_by_id(student_id):
    """الحصول على بيانات طالب بواسطة المعرف"""
    return Student.query.get(student_id)

def get_student_by_national_id(national_id):
    """الحصول على بيانات طالب بواسطة الرقم الوطني"""
    return Student.query.filter_by(national_id=national_id).first()

def get_student_by_secret_number(secret_number):
    """الحصول على بيانات طالب بواسطة الرقم السري"""
    return Student.query.filter_by(secret_number=secret_number).first()

def search_students_by_name(name):
    """البحث عن الطلاب بواسطة الاسم"""
    return Student.query.filter(Student.full_name.like(f'%{name}%')).all()

# وظائف التعامل مع الدرجات
def add_grade(student_id, class_subject_id, period_id, classwork_mark, exam_mark):
    """إضافة درجة جديدة"""
    # الحصول على معلومات المادة في الصف
    class_subject = ClassSubject.query.get(class_subject_id)
    if not class_subject:
        return None

    # حساب الدرجة الكلية
    total_mark = classwork_mark + exam_mark

    # التحقق من حالة النجاح
    is_passed = check_passing_status(classwork_mark, exam_mark, class_subject)

    # التحقق من وجود درجة سابقة
    existing_grade = Grade.query.filter_by(
        student_id=student_id,
        class_subject_id=class_subject_id,
        period_id=period_id
    ).first()

    if existing_grade:
        # تحديث الدرجة الموجودة
        existing_grade.classwork_mark = classwork_mark
        existing_grade.exam_mark = exam_mark
        existing_grade.total_mark = total_mark
        existing_grade.is_passed = is_passed
        grade = existing_grade
    else:
        # إنشاء درجة جديدة
        grade = Grade(
            student_id=student_id,
            class_subject_id=class_subject_id,
            period_id=period_id,
            classwork_mark=classwork_mark,
            exam_mark=exam_mark,
            total_mark=total_mark,
            is_passed=is_passed
        )
        db.session.add(grade)

    db.session.commit()
    return grade

def add_second_chance_grade(grade_id, second_chance_exam_mark):
    """إضافة درجة امتحان الدور الثاني"""
    grade = Grade.query.get(grade_id)
    if not grade:
        return None

    # الحصول على معلومات المادة في الصف
    class_subject = ClassSubject.query.get(grade.class_subject_id)
    if not class_subject:
        return None

    # تحديث درجة امتحان الدور الثاني
    grade.second_chance_exam_mark = second_chance_exam_mark

    # حساب الدرجة الكلية الجديدة
    grade.total_mark = grade.classwork_mark + second_chance_exam_mark

    # التحقق من حالة النجاح
    grade.is_passed = check_passing_status(grade.classwork_mark, second_chance_exam_mark, class_subject)

    db.session.commit()
    return grade

def check_passing_status(classwork_mark, exam_mark, class_subject):
    """التحقق من حالة النجاح"""
    if class_subject.daily_evaluation_only:
        # المواد التي تقيم بالتقييم اليومي فقط
        total_mark = classwork_mark + exam_mark
        return total_mark >= (class_subject.total_mark * (class_subject.passing_percentage / 100))

    # شروط النجاح العادية
    # 1. الحصول على الحد الأدنى من درجة الامتحان
    max_exam = class_subject.total_mark * (class_subject.exam_percentage / 100)
    min_exam_required = max_exam * (class_subject.min_exam_percentage / 100)
    if exam_mark < min_exam_required:
        return False

    # 2. مجموع الأعمال والامتحان يجب أن يكون >= نسبة النجاح من الدرجة الكلية
    total_mark = classwork_mark + exam_mark
    return total_mark >= (class_subject.total_mark * (class_subject.passing_percentage / 100))

def get_failed_students():
    """الحصول على قائمة الطلاب المتعثرين"""
    return db.session.query(Student).join(Grade).filter(Grade.is_passed == False).distinct().all()
