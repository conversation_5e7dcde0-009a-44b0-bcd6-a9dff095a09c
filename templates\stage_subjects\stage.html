{% extends 'base.html' %}

{% block title %}المواد الدراسية - {{ stage.name }} - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}المواد الدراسية - {{ stage.name }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <span>المواد الدراسية في مرحلة {{ stage.name }}</span>
                <a href="{{ url_for('stage_subjects.index') }}" class="btn btn-sm btn-light">
                    <i class="fas fa-arrow-right me-1"></i>
                    رجوع
                </a>
            </div>
            <div class="card-body">
                {% if stage_subjects %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم المادة</th>
                                <th>الدرجة الكلية</th>
                                <th>أعمال الفصل</th>
                                <th>الامتحان</th>
                                <th>نسبة النجاح</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for ss in stage_subjects %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ ss.subject.name }}</td>
                                <td>{{ ss.total_mark }}</td>
                                <td>{{ ss.classwork_percentage }}%</td>
                                <td>{{ ss.exam_percentage }}%</td>
                                <td>{{ ss.passing_percentage }}%</td>
                                <td>
                                    {% if ss.is_active %}
                                    <span class="badge bg-success">مفعلة</span>
                                    {% else %}
                                    <span class="badge bg-danger">غير مفعلة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('stage_subjects.edit', stage_subject_id=ss.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ url_for('stage_subjects.delete', stage_subject_id=ss.id) }}" method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه المادة من المرحلة؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                            <button type="submit" class="btn btn-sm btn-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد مواد دراسية مضافة لهذه المرحلة حالياً.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مادة دراسية للمرحلة
                </h5>
            </div>
            <div class="card-body">
                {% if available_subjects %}
                <form action="{{ url_for('stage_subjects.add') }}" method="post">
                    <input type="hidden" name="stage_id" value="{{ stage.id }}">

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="subject_id" class="form-label">المادة الدراسية <span class="text-danger">*</span></label>
                            <select class="form-select" id="subject_id" name="subject_id" required>
                                <option value="">-- اختر المادة --</option>
                                {% for subject in available_subjects %}
                                <option value="{{ subject.id }}">{{ subject.name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="col-md-6">
                            <label for="total_mark" class="form-label">الدرجة الكلية <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="total_mark" name="total_mark" min="1" step="0.5" value="100" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="classwork_percentage" class="form-label">نسبة أعمال الفصل (%) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="classwork_percentage" name="classwork_percentage" min="0" max="100" step="0.1" value="40" required>
                        </div>

                        <div class="col-md-6">
                            <label for="exam_percentage" class="form-label">نسبة الامتحان (%) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="exam_percentage" name="exam_percentage" min="0" max="100" step="0.1" value="60" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="passing_percentage" class="form-label">نسبة النجاح (%) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="passing_percentage" name="passing_percentage" min="0" max="100" step="0.1" value="50" required>
                        </div>

                        <div class="col-md-6">
                            <label for="min_exam_percentage" class="form-label">الحد الأدنى لنسبة النجاح في الامتحان (%) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="min_exam_percentage" name="min_exam_percentage" min="0" max="100" step="0.1" value="40" required>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="daily_evaluation_only" name="daily_evaluation_only">
                        <label class="form-check-label" for="daily_evaluation_only">تقييم يومي فقط (للصفوف 1-3)</label>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> مجموع نسبة أعمال الفصل ونسبة الامتحان يجب أن يساوي 100%.
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة المادة
                    </button>
                </form>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    لا توجد مواد دراسية متاحة للإضافة. يرجى إضافة مواد دراسية جديدة أولاً.
                    <a href="{{ url_for('subjects.add') }}" class="alert-link">إضافة مادة جديدة</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener("DOMContentLoaded", function() {
        // التحقق من مجموع النسب المئوية
        var classworkPercentage = document.getElementById("classwork_percentage");
        var examPercentage = document.getElementById("exam_percentage");

        function updatePercentages() {
            var classwork = parseFloat(classworkPercentage.value) || 0;
            var exam = parseFloat(examPercentage.value) || 0;
            var total = classwork + exam;

            if (total != 100) {
                classworkPercentage.classList.add("is-invalid");
                examPercentage.classList.add("is-invalid");
            } else {
                classworkPercentage.classList.remove("is-invalid");
                examPercentage.classList.remove("is-invalid");
            }
        }

        classworkPercentage.addEventListener("input", function() {
            var classwork = parseFloat(classworkPercentage.value) || 0;
            examPercentage.value = (100 - classwork).toFixed(1);
            updatePercentages();
        });

        examPercentage.addEventListener("input", function() {
            var exam = parseFloat(examPercentage.value) || 0;
            classworkPercentage.value = (100 - exam).toFixed(1);
            updatePercentages();
        });
    });
</script>
{% endblock %}
