<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة درجات الطلبة</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

        body {
            font-family: 'Tajawal', sans-serif;
        }

        .rtl {
            direction: rtl;
        }

        .ltr {
            direction: ltr;
        }
    </style>
    {% block styles %}{% endblock %}
</head>
<body class="bg-gray-100 rtl">
    <!-- Navbar -->
    <nav class="bg-blue-600 text-white shadow-md">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="text-xl font-bold">نظام إدارة درجات الطلبة</div>
                <div class="hidden md:flex space-x-4 space-x-reverse">
                    <a href="{{ url_for('index') }}" class="px-3 py-2 rounded hover:bg-blue-700">الرئيسية</a>
                    <a href="{{ url_for('list_students') }}" class="px-3 py-2 rounded hover:bg-blue-700">الطلاب</a>
                    <a href="{{ url_for('list_grades') }}" class="px-3 py-2 rounded hover:bg-blue-700">الصفوف الدراسية</a>
                    <a href="{{ url_for('list_classes') }}" class="px-3 py-2 rounded hover:bg-blue-700">الفصول</a>
                    <a href="{{ url_for('list_subjects') }}" class="px-3 py-2 rounded hover:bg-blue-700">المواد</a>
                    <a href="{{ url_for('manage_periods') }}" class="px-3 py-2 rounded hover:bg-blue-700">الفترات الدراسية</a>
                    <a href="{{ url_for('record_grades') }}" class="px-3 py-2 rounded hover:bg-blue-700">رصد الدرجات</a>
                    <a href="{{ url_for('combine_semesters_all') }}" class="px-3 py-2 rounded hover:bg-blue-700">تجميع الفصلين - جميع المواد</a>
                    <a href="{{ url_for('single_semester') }}" class="px-3 py-2 rounded hover:bg-blue-700">فصل واحد - جميع المواد</a>
                    <a href="{{ url_for('second_chance_students') }}" class="px-3 py-2 rounded hover:bg-blue-700">الدور الثاني</a>
                    <a href="{{ url_for('exam_prep') }}" class="px-3 py-2 rounded hover:bg-blue-700">التجهيز للامتحانات</a>
                    <a href="{{ url_for('reports') }}" class="px-3 py-2 rounded hover:bg-blue-700">التقارير</a>
                </div>
                <div class="md:hidden">
                    <button id="menu-toggle" class="px-3 py-2 rounded hover:bg-blue-700">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden">
            <div class="container mx-auto px-4 py-2 flex flex-col space-y-2">
                <a href="{{ url_for('index') }}" class="px-3 py-2 rounded hover:bg-blue-700">الرئيسية</a>
                <a href="{{ url_for('list_students') }}" class="px-3 py-2 rounded hover:bg-blue-700">الطلاب</a>
                <a href="{{ url_for('list_grades') }}" class="px-3 py-2 rounded hover:bg-blue-700">الصفوف الدراسية</a>
                <a href="{{ url_for('list_classes') }}" class="px-3 py-2 rounded hover:bg-blue-700">الفصول</a>
                <a href="{{ url_for('list_subjects') }}" class="px-3 py-2 rounded hover:bg-blue-700">المواد</a>
                <a href="{{ url_for('manage_periods') }}" class="px-3 py-2 rounded hover:bg-blue-700">الفترات الدراسية</a>
                <a href="{{ url_for('record_grades') }}" class="px-3 py-2 rounded hover:bg-blue-700">رصد الدرجات</a>
                <a href="{{ url_for('combine_semesters_all') }}" class="px-3 py-2 rounded hover:bg-blue-700">تجميع الفصلين - جميع المواد</a>
                <a href="{{ url_for('single_semester') }}" class="px-3 py-2 rounded hover:bg-blue-700">فصل واحد - جميع المواد</a>
                <a href="{{ url_for('second_chance_students') }}" class="px-3 py-2 rounded hover:bg-blue-700">الدور الثاني</a>
                <a href="{{ url_for('exam_prep') }}" class="px-3 py-2 rounded hover:bg-blue-700">التجهيز للامتحانات</a>
                <a href="{{ url_for('reports') }}" class="px-3 py-2 rounded hover:bg-blue-700">التقارير</a>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mx-auto px-4 py-2">
                {% for category, message in messages %}
                    <div class="{% if category == 'error' %}bg-red-100 border border-red-400 text-red-700{% else %}bg-green-100 border border-green-400 text-green-700{% endif %} px-4 py-3 rounded relative mb-4" role="alert">
                        <span class="block sm:inline">{{ message }}</span>
                        <button type="button" class="absolute top-0 bottom-0 left-0 px-4 py-3 close-alert">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-6">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-4">
        <div class="container mx-auto px-4 text-center">
            <p>جميع الحقوق محفوظة &copy; {{ now.year }} - نظام إدارة درجات الطلبة</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Toggle Mobile Menu
        document.getElementById('menu-toggle').addEventListener('click', function() {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });

        // Close Alert Messages
        document.querySelectorAll('.close-alert').forEach(function(button) {
            button.addEventListener('click', function() {
                this.parentElement.style.display = 'none';
            });
        });


    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
