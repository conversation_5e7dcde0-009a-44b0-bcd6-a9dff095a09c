import webview
from flask import Flask
from app_config import configure_app
from app_routes import register_routes
from models import db, init_db

# إنشاء تطبيق Flask
app = Flask(__name__)

# تكوين التطبيق
configure_app(app)

# تسجيل المسارات
register_routes(app)

# تهيئة قاعدة البيانات
init_db(app)

def start_app():
    # تشغيل تطبيق Flask في خادم تطوير
    return app

if __name__ == '__main__':
    # إنشاء نافذة تطبيق سطح المكتب
    webview.create_window('نظام إدارة درجات الطلبة', start_app(), width=1200, height=800)
    webview.start(debug=True)
