{% extends 'base.html' %}

{% block title %}رصد درجة - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}رصد درجة: {{ subject.name }} - {{ period.name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-graduate me-2"></i>
                    بيانات الطالب
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 30%">الاسم الكامل</th>
                        <td>{{ student.full_name }}</td>
                    </tr>
                    <tr>
                        <th>الرقم الوطني</th>
                        <td>{{ student.national_id }}</td>
                    </tr>
                    <tr>
                        <th>الصف</th>
                        <td>{{ student.class_obj.name }} ({{ student.class_obj.stage.name }})</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    رصد الدرجة
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('grades.add_student_grade', student_id=student.id, subject_id=subject.id, period_id=period.id) }}" method="post">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="classworkMark" class="form-label">درجة أعمال الفصل <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="classworkMark" name="classwork_mark" min="0" max="{{ max_classwork }}" step="0.5" value="{{ grade_info.classwork }}" required>
                                <span class="input-group-text">من {{ max_classwork }}</span>
                            </div>
                            <input type="hidden" id="maxClasswork" value="{{ max_classwork }}">
                        </div>

                        <div class="col-md-6">
                            <label for="examMark" class="form-label">درجة الامتحان <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="examMark" name="exam_mark" min="0" max="{{ max_exam }}" step="0.5" value="{{ grade_info.exam }}" required>
                                <span class="input-group-text">من {{ max_exam }}</span>
                            </div>
                            <input type="hidden" id="maxExam" value="{{ max_exam }}">
                        </div>
                    </div>

                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6 class="card-title">ملخص الدرجة</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <p class="mb-1">المجموع:</p>
                                    <h5 id="totalMark" class="text-primary">{{ grade_info.total }}</h5>
                                </div>
                                <div class="col-md-4">
                                    <p class="mb-1">النسبة المئوية:</p>
                                    <h5 id="percentage" class="text-primary">{{ (grade_info.total / (max_classwork + max_exam) * 100)|round(2) }}%</h5>
                                </div>
                                <div class="col-md-4">
                                    <p class="mb-1">النتيجة:</p>
                                    <h5 id="result" class="{% if grade_info.is_passed %}text-success{% else %}text-danger{% endif %}">
                                        {% if grade_info.is_passed %}ناجح{% else %}راسب{% endif %}
                                    </h5>
                                </div>
                            </div>

                            <div class="alert alert-info mt-3 mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>شروط النجاح:</strong>
                                <ul class="mb-0">
                                    <li>الحصول على 40% من درجة الامتحان على الأقل.</li>
                                    <li>مجموع (أعمال الفصل + الامتحان) يجب أن يكون 50% من الدرجة الكلية على الأقل.</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('grades.student_grades', student_id=student.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            رجوع
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ الدرجة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener("DOMContentLoaded", function() {
        // حساب المجموع والنتيجة تلقائياً عند تغيير الدرجات
        var classworkMark = document.getElementById("classworkMark");
        var examMark = document.getElementById("examMark");

        function calculateGrade() {
            var classwork = parseFloat(classworkMark.value) || 0;
            var exam = parseFloat(examMark.value) || 0;
            var maxClasswork = parseFloat(document.getElementById("maxClasswork").value) || 0;
            var maxExam = parseFloat(document.getElementById("maxExam").value) || 0;
            var total = classwork + exam;
            var maxTotal = maxClasswork + maxExam;
            var percentage = (total / maxTotal) * 100;

            // عرض المجموع والنسبة المئوية
            document.getElementById("totalMark").textContent = total.toFixed(2);
            document.getElementById("percentage").textContent = percentage.toFixed(2) + "%";

            // تحديد النتيجة
            var resultElement = document.getElementById("result");
            var examPercentage = (exam / maxExam) * 100;

            if (examPercentage < 40) {
                resultElement.textContent = "راسب";
                resultElement.className = "text-danger";
            } else if (percentage < 50) {
                resultElement.textContent = "راسب";
                resultElement.className = "text-danger";
            } else {
                resultElement.textContent = "ناجح";
                resultElement.className = "text-success";
            }
        }

        classworkMark.addEventListener("input", calculateGrade);
        examMark.addEventListener("input", calculateGrade);
    });
</script>
{% endblock %}
