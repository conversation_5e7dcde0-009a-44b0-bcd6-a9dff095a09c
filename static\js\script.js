// ===== وظائف عامة =====

// تهيئة التلميحات
function initTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// تأكيد الحذف
function confirmDelete(event, message) {
    if (!confirm(message || 'هل أنت متأكد من الحذف؟')) {
        event.preventDefault();
        return false;
    }
    return true;
}

// البحث في الجدول
function searchTable() {
    var input, filter, table, tr, td, i, j, txtValue, found;
    input = document.getElementById("searchInput");
    filter = input.value.toUpperCase();
    table = document.getElementById("dataTable");
    tr = table.getElementsByTagName("tr");

    for (i = 1; i < tr.length; i++) {
        found = false;
        td = tr[i].getElementsByTagName("td");
        for (j = 0; j < td.length; j++) {
            if (td[j]) {
                txtValue = td[j].textContent || td[j].innerText;
                if (txtValue.toUpperCase().indexOf(filter) > -1) {
                    found = true;
                    break;
                }
            }
        }
        if (found) {
            tr[i].style.display = "";
        } else {
            tr[i].style.display = "none";
        }
    }
}

// ===== وظائف خاصة بالطلاب =====

// البحث عن الطلاب باستخدام AJAX
function searchStudents() {
    var searchType = document.getElementById("searchType").value;
    var searchValue = document.getElementById("searchValue").value;

    if (!searchValue) {
        return;
    }

    // إظهار مؤشر التحميل
    document.getElementById("searchResults").innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري البحث...</span></div></div>';

    // إرسال طلب AJAX
    $.ajax({
        url: '/students/api/search',
        type: 'GET',
        data: {
            type: searchType,
            query: searchValue
        },
        success: function(data) {
            var resultsHtml = '';

            if (data.length === 0) {
                resultsHtml = '<div class="alert alert-info">لم يتم العثور على نتائج</div>';
            } else {
                resultsHtml = '<div class="table-responsive"><table class="table table-hover"><thead><tr><th>#</th><th>الاسم</th><th>الرقم الوطني</th><th>الصف</th><th>الإجراءات</th></tr></thead><tbody>';

                for (var i = 0; i < data.length; i++) {
                    var student = data[i];
                    resultsHtml += '<tr>';
                    resultsHtml += '<td>' + (i + 1) + '</td>';
                    resultsHtml += '<td>' + student.full_name + '</td>';
                    resultsHtml += '<td>' + student.national_id + '</td>';
                    resultsHtml += '<td>' + student.class_name + '</td>';
                    resultsHtml += '<td>';
                    resultsHtml += '<a href="/grades/student/' + student.id + '" class="btn btn-sm btn-primary me-1"><i class="fas fa-chart-bar"></i> الدرجات</a>';
                    resultsHtml += '<a href="/students/edit/' + student.id + '" class="btn btn-sm btn-warning me-1"><i class="fas fa-edit"></i> تعديل</a>';
                    resultsHtml += '</td>';
                    resultsHtml += '</tr>';
                }

                resultsHtml += '</tbody></table></div>';
            }

            document.getElementById("searchResults").innerHTML = resultsHtml;
        },
        error: function() {
            document.getElementById("searchResults").innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء البحث</div>';
        }
    });
}

// ===== وظائف خاصة بالدرجات =====

// حساب المجموع والنتيجة تلقائياً
function calculateGrade() {
    var classworkMark = parseFloat(document.getElementById("classworkMark").value) || 0;
    var examMark = parseFloat(document.getElementById("examMark").value) || 0;
    var maxClasswork = parseFloat(document.getElementById("maxClasswork").value) || 0;
    var maxExam = parseFloat(document.getElementById("maxExam").value) || 0;
    var totalMark = classworkMark + examMark;
    var maxTotal = maxClasswork + maxExam;
    var percentage = (totalMark / maxTotal) * 100;

    // التحقق من صحة الدرجات
    if (classworkMark > maxClasswork) {
        document.getElementById("classworkMark").value = maxClasswork;
        classworkMark = maxClasswork;
    }

    if (examMark > maxExam) {
        document.getElementById("examMark").value = maxExam;
        examMark = maxExam;
    }

    // عرض المجموع والنسبة المئوية
    document.getElementById("totalMark").textContent = totalMark.toFixed(2);
    document.getElementById("percentage").textContent = percentage.toFixed(2) + "%";

    // تحديد النتيجة
    var resultElement = document.getElementById("result");
    var examPercentage = (examMark / maxExam) * 100;

    if (examPercentage < 40) {
        resultElement.textContent = "راسب";
        resultElement.className = "text-danger";
    } else if (percentage < 50) {
        resultElement.textContent = "راسب";
        resultElement.className = "text-danger";
    } else {
        resultElement.textContent = "ناجح";
        resultElement.className = "text-success";
    }
}

// تهيئة نوافذ تأكيد الحذف
function initDeleteModals() {
    // تهيئة نوافذ تأكيد الحذف
    var deleteModals = document.querySelectorAll('.modal');
    deleteModals.forEach(function(modal) {
        // إعادة تعيين حالة النافذة عند إغلاقها
        modal.addEventListener('hidden.bs.modal', function() {
            // إعادة تعيين محتوى النافذة إذا لزم الأمر
            var form = modal.querySelector('form');
            if (form) {
                form.reset();
            }
        });

        // منع الارتعاش عند النقر داخل النافذة
        modal.addEventListener('click', function(event) {
            if (event.target.closest('.modal-content')) {
                event.stopPropagation();
            }
        });
    });

    // معالجة نماذج الحذف
    var deleteForms = document.querySelectorAll('.delete-form');
    deleteForms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            // إغلاق النافذة المنبثقة قبل إرسال النموذج
            var modal = form.closest('.modal');
            if (modal) {
                var modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        });
    });
}

// ===== تهيئة الصفحة =====
document.addEventListener("DOMContentLoaded", function() {
    // تهيئة التلميحات
    initTooltips();

    // تهيئة نوافذ تأكيد الحذف
    initDeleteModals();

    // تهيئة أحداث تأكيد الحذف للأزرار التي لا تستخدم نوافذ Bootstrap
    var deleteButtons = document.querySelectorAll(".btn-delete:not([data-bs-toggle='modal'])");
    deleteButtons.forEach(function(button) {
        button.addEventListener("click", function(event) {
            return confirmDelete(event, button.getAttribute("data-confirm-message"));
        });
    });

    // تهيئة البحث في الجدول
    var searchInput = document.getElementById("searchInput");
    if (searchInput) {
        searchInput.addEventListener("keyup", searchTable);
    }

    // تهيئة حساب الدرجات
    var gradeInputs = document.querySelectorAll("#classworkMark, #examMark");
    gradeInputs.forEach(function(input) {
        if (input) {
            input.addEventListener("input", calculateGrade);
            // حساب الدرجات عند تحميل الصفحة
            calculateGrade();
        }
    });
});
