{% extends 'base.html' %}

{% block title %}إدارة الصفوف والمراحل - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}إدارة الصفوف والمراحل الدراسية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <h3>المراحل الدراسية</h3>
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <span>قائمة المراحل الدراسية</span>
                <a href="{{ url_for('classes.add_stage') }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus me-1"></i>
                    إضافة مرحلة جديدة
                </a>
            </div>
            <div class="card-body">
                {% if stages %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم المرحلة</th>
                                <th>عدد الصفوف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stage in stages %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ stage.name }}</td>
                                <td>{{ stage.classes|length }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('classes.edit_stage', stage_id=stage.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if not stage.classes %}
                                        <form action="{{ url_for('classes.delete_stage', stage_id=stage.id) }}" method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف المرحلة: {{ stage.name }}؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                            <button type="submit" class="btn btn-sm btn-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        {% else %}
                                        <button type="button" class="btn btn-sm btn-danger" title="حذف" disabled data-bs-toggle="tooltip" data-bs-title="لا يمكن حذف هذه المرحلة لأنها تحتوي على {{ stage.classes|length }} صف/صفوف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد مراحل دراسية مسجلة حالياً.
                    <a href="{{ url_for('classes.add_stage') }}" class="alert-link">إضافة مرحلة جديدة</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <h3>الصفوف الدراسية</h3>
        <div class="card">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <span>قائمة الصفوف الدراسية</span>
                <a href="{{ url_for('classes.add_class') }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus me-1"></i>
                    إضافة صف جديد
                </a>
            </div>
            <div class="card-body">
                {% if classes %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم الصف</th>
                                <th>المرحلة</th>
                                <th>عدد الطلاب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for class_obj in classes %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ class_obj.name }}</td>
                                <td>{{ class_obj.stage.name }}</td>
                                <td>{{ class_obj.students|length }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('classes.periods', class_id=class_obj.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="الفترات الدراسية">
                                            <i class="fas fa-calendar"></i>
                                        </a>
                                        <a href="{{ url_for('classes.edit_class', class_id=class_obj.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if not class_obj.students %}
                                        <form action="{{ url_for('classes.delete_class', class_id=class_obj.id) }}" method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف الصف: {{ class_obj.name }}؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                            <button type="submit" class="btn btn-sm btn-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        {% else %}
                                        <button type="button" class="btn btn-sm btn-danger" title="حذف" disabled data-bs-toggle="tooltip" data-bs-title="لا يمكن حذف هذا الصف لأنه يحتوي على {{ class_obj.students|length }} طالب/طلاب">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد صفوف دراسية مسجلة حالياً.
                    <a href="{{ url_for('classes.add_class') }}" class="alert-link">إضافة صف جديد</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
