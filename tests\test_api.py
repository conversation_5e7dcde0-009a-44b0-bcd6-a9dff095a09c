import requests
import json

try:
    # محاولة الاتصال بـ API
    response = requests.get('http://127.0.0.1:5000/grades/combine-semesters/all-data?class_id=1')
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print("\nData received:")
        print(json.dumps(data, indent=2, ensure_ascii=False))
        
        # تحقق من وجود البيانات
        if 'students' in data:
            print(f"\nNumber of students: {len(data['students'])}")
        if 'subjects' in data:
            print(f"Number of subjects: {len(data['subjects'])}")
    else:
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"Connection error: {e}")