{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">تصدير البيانات</h1>
        <a href="{{ url_for('reports') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-arrow-right ml-1"></i> العودة
        </a>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- بطاقة تصدير بيانات الطلاب -->
        <div class="bg-blue-50 rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center mb-4">
                <div class="bg-blue-500 text-white p-3 rounded-full mr-4">
                    <i class="fas fa-user-graduate text-xl"></i>
                </div>
                <h2 class="text-xl font-semibold">تصدير بيانات الطلاب</h2>
            </div>
            <p class="text-gray-600 mb-4">تصدير قائمة الطلاب مع بياناتهم الأساسية بصيغة Excel.</p>
            
            <form id="export-students-form" class="mb-4">
                <div class="mb-4">
                    <label for="class-select-students" class="block text-gray-700 font-medium mb-2">الفصل</label>
                    <select id="class-select-students" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="" selected>جميع الفصول</option>
                        {% for class in classes %}
                        <option value="{{ class.id }}">{{ class.name }} - {{ class.grade.name }} ({{ class.grade.track }})</option>
                        {% endfor %}
                    </select>
                </div>
                
                <button type="button" id="export-students-btn" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition-colors">
                    <i class="fas fa-file-export ml-1"></i> تصدير بيانات الطلاب
                </button>
            </form>
        </div>
        
        <!-- بطاقة تصدير بيانات الدرجات -->
        <div class="bg-green-50 rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center mb-4">
                <div class="bg-green-500 text-white p-3 rounded-full mr-4">
                    <i class="fas fa-clipboard-check text-xl"></i>
                </div>
                <h2 class="text-xl font-semibold">تصدير بيانات الدرجات</h2>
            </div>
            <p class="text-gray-600 mb-4">تصدير درجات الطلاب في المواد المختلفة بصيغة Excel.</p>
            
            <form id="export-grades-form" class="mb-4">
                <div class="mb-4">
                    <label for="class-select-grades" class="block text-gray-700 font-medium mb-2">الفصل</label>
                    <select id="class-select-grades" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="" selected>جميع الفصول</option>
                        {% for class in classes %}
                        <option value="{{ class.id }}">{{ class.name }} - {{ class.grade.name }} ({{ class.grade.track }})</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="mb-4">
                    <label for="period-select-grades" class="block text-gray-700 font-medium mb-2">الفترة</label>
                    <select id="period-select-grades" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="" selected>جميع الفترات</option>
                        {% for period in periods %}
                        <option value="{{ period.id }}">{{ period.name }} - {{ period.academic_year }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <button type="button" id="export-grades-btn" class="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded transition-colors">
                    <i class="fas fa-file-export ml-1"></i> تصدير بيانات الدرجات
                </button>
            </form>
        </div>
        
        <!-- بطاقة تصدير بيانات الأرقام السرية وأرقام الجلوس -->
        <div class="bg-purple-50 rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center mb-4">
                <div class="bg-purple-500 text-white p-3 rounded-full mr-4">
                    <i class="fas fa-key text-xl"></i>
                </div>
                <h2 class="text-xl font-semibold">تصدير الأرقام السرية وأرقام الجلوس</h2>
            </div>
            <p class="text-gray-600 mb-4">تصدير الأرقام السرية وأرقام الجلوس للطلاب بصيغة Excel.</p>
            
            <form id="export-numbers-form" class="mb-4">
                <div class="mb-4">
                    <label for="class-select-numbers" class="block text-gray-700 font-medium mb-2">الفصل</label>
                    <select id="class-select-numbers" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="" selected>جميع الفصول</option>
                        {% for class in classes %}
                        <option value="{{ class.id }}">{{ class.name }} - {{ class.grade.name }} ({{ class.grade.track }})</option>
                        {% endfor %}
                    </select>
                </div>
                
                <button type="button" id="export-numbers-btn" class="w-full bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 rounded transition-colors">
                    <i class="fas fa-file-export ml-1"></i> تصدير الأرقام السرية وأرقام الجلوس
                </button>
            </form>
        </div>
        
        <!-- بطاقة تصدير جميع البيانات -->
        <div class="bg-yellow-50 rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center mb-4">
                <div class="bg-yellow-500 text-white p-3 rounded-full mr-4">
                    <i class="fas fa-database text-xl"></i>
                </div>
                <h2 class="text-xl font-semibold">تصدير جميع البيانات</h2>
            </div>
            <p class="text-gray-600 mb-4">تصدير جميع بيانات النظام بصيغة Excel.</p>
            
            <button type="button" id="export-all-btn" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-2 px-4 rounded transition-colors">
                <i class="fas fa-file-export ml-1"></i> تصدير جميع البيانات
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تصدير بيانات الطلاب
    document.getElementById('export-students-btn').addEventListener('click', function() {
        const classId = document.getElementById('class-select-students').value;
        let url = '/api/export/students';
        
        if (classId) {
            url += `?class_id=${classId}`;
        }
        
        window.location.href = url;
    });
    
    // تصدير بيانات الدرجات
    document.getElementById('export-grades-btn').addEventListener('click', function() {
        const classId = document.getElementById('class-select-grades').value;
        const periodId = document.getElementById('period-select-grades').value;
        let url = '/api/export/grades';
        
        const params = [];
        if (classId) params.push(`class_id=${classId}`);
        if (periodId) params.push(`period_id=${periodId}`);
        
        if (params.length > 0) {
            url += '?' + params.join('&');
        }
        
        window.location.href = url;
    });
    
    // تصدير الأرقام السرية وأرقام الجلوس
    document.getElementById('export-numbers-btn').addEventListener('click', function() {
        const classId = document.getElementById('class-select-numbers').value;
        let url = '/api/export/numbers';
        
        if (classId) {
            url += `?class_id=${classId}`;
        }
        
        window.location.href = url;
    });
    
    // تصدير جميع البيانات
    document.getElementById('export-all-btn').addEventListener('click', function() {
        window.location.href = '/api/export/all';
    });
</script>
{% endblock %}
