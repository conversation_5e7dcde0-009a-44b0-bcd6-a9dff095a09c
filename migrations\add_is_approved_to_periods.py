from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from models import db
import sqlite3
import os

def run_migration():
    print("بدء تنفيذ الهجرة: إضافة حقل is_approved إلى جدول periods...")

    # استخدام sqlite3 مباشرة بدلاً من SQLAlchemy
    try:
        # الحصول على مسار قاعدة البيانات
        # البحث عن ملف قاعدة البيانات في المجلد الحالي والمجلدات الفرعية
        db_path = None
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.db'):
                    db_path = os.path.join(root, file)
                    print(f"تم العثور على قاعدة البيانات في المسار: {db_path}")
                    break
            if db_path:
                break

        # التحقق من وجود قاعدة البيانات
        if not db_path:
            print("لم يتم العثور على قاعدة البيانات في أي مكان.")
            return False

        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(periods)")
        columns = [row[1] for row in cursor.fetchall()]

        if 'is_approved' in columns:
            print("العمود is_approved موجود بالفعل في جدول periods.")
            conn.close()
            return True

        print(f"العمود is_approved غير موجود في جدول periods. سيتم إضافته الآن.")

        # إضافة العمود إلى الجدول
        cursor.execute('ALTER TABLE periods ADD COLUMN is_approved BOOLEAN DEFAULT FALSE')
        conn.commit()
        print("تم إضافة العمود is_approved إلى جدول periods بنجاح.")

        # إغلاق الاتصال
        conn.close()

    except Exception as e:
        print(f"حدث خطأ أثناء تنفيذ الهجرة: {e}")
        return False

    print("اكتملت الهجرة بنجاح.")
    return True

if __name__ == "__main__":
    # إنشاء تطبيق Flask مؤقت لتنفيذ الهجرة
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///grades.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # تهيئة قاعدة البيانات
    db.init_app(app)

    with app.app_context():
        run_migration()
