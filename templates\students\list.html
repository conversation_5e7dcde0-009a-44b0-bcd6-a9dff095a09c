{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">إدارة الطلاب</h1>
        <a href="{{ url_for('add_student') }}" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-plus ml-1"></i> إضافة طالب جديد
        </a>
    </div>
    
    <!-- اختيار الفصل -->
    <div class="mb-6 bg-gray-50 p-4 rounded-lg">
        <h2 class="text-lg font-semibold mb-3">اختر الفصل</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            {% for class in classes %}
            <a href="{{ url_for('list_students', class_id=class.id) }}" class="block p-3 rounded-lg border {% if selected_class and selected_class.id == class.id %}bg-blue-100 border-blue-500{% else %}bg-white hover:bg-gray-100 border-gray-200{% endif %} transition-colors">
                <div class="font-medium">{{ class.name }}</div>
                <div class="text-sm text-gray-600">{{ class.grade.name }} - {{ class.grade.track }}</div>
            </a>
            {% else %}
            <div class="col-span-3 text-center py-4 text-gray-500">
                لا توجد فصول مسجلة. يرجى إضافة فصول أولاً.
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- قائمة الطلاب -->
    {% if selected_class %}
    <div>
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">طلاب {{ selected_class.name }}</h2>
            <div class="relative">
                <input type="text" id="search-input" placeholder="بحث بالاسم أو الرقم الوطني" class="border border-gray-300 rounded-lg py-2 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="py-3 px-4 border-b text-right">#</th>
                        <th class="py-3 px-4 border-b text-right">الاسم</th>
                        <th class="py-3 px-4 border-b text-right">الرقم الوطني</th>
                        <th class="py-3 px-4 border-b text-right">الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="students-table-body">
                    {% for student in students %}
                    <tr class="hover:bg-gray-50">
                        <td class="py-3 px-4 border-b">{{ loop.index }}</td>
                        <td class="py-3 px-4 border-b">{{ student.name }}</td>
                        <td class="py-3 px-4 border-b">{{ student.national_id }}</td>
                        <td class="py-3 px-4 border-b">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="{{ url_for('edit_student', id=student.id) }}" class="text-blue-500 hover:text-blue-700">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="text-red-500 hover:text-red-700 delete-student" data-id="{{ student.id }}" data-name="{{ student.name }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="4" class="py-6 text-center text-gray-500">لا يوجد طلاب مسجلين في هذا الفصل</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="text-center py-10 text-gray-500">
        <i class="fas fa-users text-5xl mb-4 text-gray-300"></i>
        <p>يرجى اختيار فصل لعرض الطلاب</p>
    </div>
    {% endif %}
</div>

<!-- نموذج حذف الطالب -->
<div id="delete-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
        <h3 class="text-xl font-bold mb-4">تأكيد الحذف</h3>
        <p class="mb-6">هل أنت متأكد من حذف الطالب <span id="student-name" class="font-semibold"></span>؟</p>
        <div class="flex justify-end space-x-4 space-x-reverse">
            <button id="cancel-delete" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded transition-colors">
                إلغاء
            </button>
            <form id="delete-form" method="POST">
                <button type="submit" class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded transition-colors">
                    حذف
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // البحث في الجدول
    document.getElementById('search-input').addEventListener('keyup', function() {
        const searchValue = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('#students-table-body tr');
        
        tableRows.forEach(row => {
            const name = row.cells[1].textContent.toLowerCase();
            const nationalId = row.cells[2].textContent.toLowerCase();
            
            if (name.includes(searchValue) || nationalId.includes(searchValue)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
    
    // حذف الطالب
    const deleteModal = document.getElementById('delete-modal');
    const studentNameSpan = document.getElementById('student-name');
    const deleteForm = document.getElementById('delete-form');
    const cancelDelete = document.getElementById('cancel-delete');
    
    document.querySelectorAll('.delete-student').forEach(button => {
        button.addEventListener('click', function() {
            const studentId = this.dataset.id;
            const studentName = this.dataset.name;
            
            studentNameSpan.textContent = studentName;
            deleteForm.action = `/students/delete/${studentId}`;
            deleteModal.classList.remove('hidden');
        });
    });
    
    cancelDelete.addEventListener('click', function() {
        deleteModal.classList.add('hidden');
    });
</script>
{% endblock %}
