from flask import Flask
from models import db, Period
from run import app

def check_periods():
    """التحقق من حالة الفترات الدراسية"""
    
    print("بدء التحقق من الفترات الدراسية...")
    
    # الحصول على جميع الفترات الدراسية
    periods = Period.query.all()
    
    print(f"عدد الفترات الدراسية: {len(periods)}")
    for period in periods:
        print(f"- {period.id}: {period.name} ({period.academic_year}), معتمدة: {'نعم' if period.is_approved else 'لا'}")
    
    # البحث عن الفترات المتعلقة بالفصل الأول والثاني
    first_periods = Period.query.filter(Period.name.like('%أول%') | Period.name.like('%أولى%')).all()
    second_periods = Period.query.filter(Period.name.like('%ثاني%') | Period.name.like('%ثانية%')).all()
    
    print("\nفترات الفصل الأول:")
    for period in first_periods:
        print(f"- {period.id}: {period.name} ({period.academic_year}), معتمدة: {'نعم' if period.is_approved else 'لا'}")
    
    print("\nفترات الفصل الثاني:")
    for period in second_periods:
        print(f"- {period.id}: {period.name} ({period.academic_year}), معتمدة: {'نعم' if period.is_approved else 'لا'}")
    
    # التحقق من العلاقة بين الفترات
    print("\nالتحقق من العلاقة بين الفترات:")
    for second_period in second_periods:
        # البحث عن الفترة السابقة (الفصل الأول)
        previous_period_name = second_period.name.replace("ثاني", "أول").replace("ثانية", "أولى")
        previous_period = Period.query.filter_by(name=previous_period_name, academic_year=second_period.academic_year).first()
        
        if previous_period:
            print(f"- الفترة: {second_period.name} ({second_period.academic_year})")
            print(f"  الفترة السابقة: {previous_period.name} ({previous_period.academic_year})")
            print(f"  حالة اعتماد الفترة السابقة: {'معتمدة' if previous_period.is_approved else 'غير معتمدة'}")
        else:
            print(f"- الفترة: {second_period.name} ({second_period.academic_year})")
            print(f"  لم يتم العثور على الفترة السابقة: {previous_period_name} ({second_period.academic_year})")
    
    print("\nتم التحقق من الفترات الدراسية بنجاح.")

if __name__ == "__main__":
    with app.app_context():
        check_periods()
