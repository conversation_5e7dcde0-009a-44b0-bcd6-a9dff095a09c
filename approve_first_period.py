from flask import Flask
from models import db, Period
from run import app

def approve_first_period():
    """اعتماد الفصل الأول"""
    
    print("بدء اعتماد الفصل الأول...")
    
    # البحث عن الفترة المتعلقة بالفصل الأول
    first_period = Period.query.filter_by(name="الفصل الأول", academic_year="2023-2024").first()
    
    if first_period:
        print(f"تم العثور على الفترة: {first_period.name} ({first_period.academic_year})")
        print(f"حالة الاعتماد الحالية: {'معتمدة' if first_period.is_approved else 'غير معتمدة'}")
        
        # اعتماد الفترة
        first_period.is_approved = True
        db.session.commit()
        
        print(f"تم اعتماد الفترة: {first_period.name} ({first_period.academic_year})")
        print(f"حالة الاعتماد الجديدة: {'معتمدة' if first_period.is_approved else 'غير معتمدة'}")
    else:
        print("لم يتم العثور على الفترة المتعلقة بالفصل الأول")
    
    print("\nتم اعتماد الفصل الأول بنجاح.")

if __name__ == "__main__":
    with app.app_context():
        approve_first_period()
