import sys
sys.path.append('.')

from app import app
import json

with app.app_context():
    try:
        print("Testing with Flask test client...")
        
        # إنشاء test client
        client = app.test_client()
        
        # إجراء طلب GET للمسار
        response = client.get('/grades/combine-semesters/all-data?class_id=1')
        
        print(f"Status Code: {response.status_code}")
        print(f"Content Type: {response.content_type}")
        
        if response.status_code == 200:
            data = response.get_json()
            print(f"Data keys: {list(data.keys()) if data else 'No data'}")
            
            if data and 'students' in data:
                print(f"Number of students: {len(data['students'])}")
            if data and 'subjects' in data:
                print(f"Number of subjects: {len(data['subjects'])}")
                
            # كتابة النتيجة في ملف
            with open('flask_client_result.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            print("Result saved to flask_client_result.json")
        else:
            print(f"Error response: {response.get_data(as_text=True)}")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        
        with open('flask_client_error.txt', 'w', encoding='utf-8') as f:
            f.write(str(e) + '\n')
            f.write(traceback.format_exc())