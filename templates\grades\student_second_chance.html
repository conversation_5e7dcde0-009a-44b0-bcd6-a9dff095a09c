{% extends 'base.html' %}

{% block title %}الدور الثاني للطالب - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}الدور الثاني للطالب: {{ student.full_name }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-graduate me-2"></i>
                    بيانات الطالب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">الاسم الكامل</th>
                                <td>{{ student.full_name }}</td>
                            </tr>
                            <tr>
                                <th>الرقم الوطني</th>
                                <td>{{ student.national_id }}</td>
                            </tr>
                            <tr>
                                <th>الصف</th>
                                <td>{{ student.class_obj.name }} ({{ student.class_obj.stage.name }})</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> هذا الطالب متعثر في {{ failed_subjects|length }} مادة/مواد ويحتاج لدخول امتحان الدور الثاني.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if failed_subjects %}
    {% for subject_id, subject_data in failed_subjects.items() %}
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-book me-2"></i>
                    {{ subject_data.subject.name }}
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>الفترة</th>
                                <th>أعمال الفصل</th>
                                <th>امتحان الفصل</th>
                                <th>امتحان الدور الثاني</th>
                                <th>المجموع</th>
                                <th>النتيجة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for period_data in subject_data.periods %}
                                <tr>
                                    <td>{{ period_data.period.name }}</td>
                                    <td>{{ "%.1f"|format(period_data.grade_info.classwork) }}</td>
                                    <td class="text-danger">{{ "%.1f"|format(period_data.grade_info.exam) }}</td>
                                    <td>
                                        {% if period_data.grade_info.second_chance_exam_mark is not none %}
                                            <span class="text-success">{{ "%.1f"|format(period_data.grade_info.second_chance_exam_mark) }}</span>
                                        {% else %}
                                            <span class="text-muted">لم يتم الرصد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if period_data.grade_info.second_chance_exam_mark is not none %}
                                            {{ "%.1f"|format(period_data.grade_info.classwork + period_data.grade_info.second_chance_exam_mark) }}
                                        {% else %}
                                            {{ "%.1f"|format(period_data.grade_info.total) }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if period_data.grade_info.is_passed %}
                                            <span class="badge bg-success">ناجح</span>
                                        {% else %}
                                            <span class="badge bg-danger">راسب</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('grades.add_second_chance_grade', grade_id=period_data.grade_info.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit me-1"></i>
                                            رصد درجة الدور الثاني
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    {% endfor %}
{% else %}
    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i>
        هذا الطالب ليس لديه مواد متعثر فيها. جميع المواد ناجح فيها.
    </div>
{% endif %}

<div class="mt-4">
    <a href="{{ url_for('grades.second_chance') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>
        رجوع
    </a>
</div>
{% endblock %}
