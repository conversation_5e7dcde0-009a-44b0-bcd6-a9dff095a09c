import os
import sys
import webview
from flask import Flask
from app_config import configure_app
from app_routes import register_routes
from models import db, init_db, Grade, Class, Subject, Period, Student
from migrations.add_is_approved_to_periods import run_migration

# إنشاء تطبيق Flask
app = Flask(__name__)

# تكوين التطبيق
configure_app(app)

# تسجيل المسارات
register_routes(app)

# تهيئة قاعدة البيانات
init_db(app)

# لا نحتاج إلى تنفيذ الهجرات هنا لأننا سنعيد إنشاء قاعدة البيانات

def initialize_sample_data():
    """تهيئة بيانات نموذجية للتطبيق"""
    with app.app_context():
        # التحقق من وجود بيانات
        if Grade.query.count() > 0:
            return

        # إنشاء الصفوف الدراسية
        grade_10 = Grade(name="الصف الأول الثانوي", level=10, track="عام")
        grade_11_science = Grade(name="الصف الثاني الثانوي", level=11, track="علمي")
        grade_11_literary = Grade(name="الصف الثاني الثانوي", level=11, track="أدبي")
        grade_12_science = Grade(name="الصف الثالث الثانوي", level=12, track="علمي")
        grade_12_literary = Grade(name="الصف الثالث الثانوي", level=12, track="أدبي")

        db.session.add_all([grade_10, grade_11_science, grade_11_literary, grade_12_science, grade_12_literary])
        db.session.commit()

        # إنشاء الفصول
        class_10a = Class(name="1/أ", grade_id=grade_10.id, academic_year="2023-2024")
        class_10b = Class(name="1/ب", grade_id=grade_10.id, academic_year="2023-2024")
        class_11_science = Class(name="2/علمي", grade_id=grade_11_science.id, academic_year="2023-2024")
        class_11_literary = Class(name="2/أدبي", grade_id=grade_11_literary.id, academic_year="2023-2024")
        class_12_science = Class(name="3/علمي", grade_id=grade_12_science.id, academic_year="2023-2024")
        class_12_literary = Class(name="3/أدبي", grade_id=grade_12_literary.id, academic_year="2023-2024")

        db.session.add_all([class_10a, class_10b, class_11_science, class_11_literary, class_12_science, class_12_literary])
        db.session.commit()

        # إنشاء المواد الدراسية
        # الصف الأول
        subject_10_math = Subject(name="الرياضيات", grade_id=grade_10.id, total_mark=100)
        subject_10_physics = Subject(name="الفيزياء", grade_id=grade_10.id, total_mark=100)
        subject_10_chemistry = Subject(name="الكيمياء", grade_id=grade_10.id, total_mark=100)
        subject_10_biology = Subject(name="الأحياء", grade_id=grade_10.id, total_mark=100)
        subject_10_arabic = Subject(name="اللغة العربية", grade_id=grade_10.id, total_mark=100)
        subject_10_english = Subject(name="اللغة الإنجليزية", grade_id=grade_10.id, total_mark=100)

        # الصف الثاني علمي
        subject_11s_math = Subject(name="الرياضيات", grade_id=grade_11_science.id, total_mark=100)
        subject_11s_physics = Subject(name="الفيزياء", grade_id=grade_11_science.id, total_mark=100)
        subject_11s_chemistry = Subject(name="الكيمياء", grade_id=grade_11_science.id, total_mark=100)
        subject_11s_biology = Subject(name="الأحياء", grade_id=grade_11_science.id, total_mark=100)
        subject_11s_arabic = Subject(name="اللغة العربية", grade_id=grade_11_science.id, total_mark=100)
        subject_11s_english = Subject(name="اللغة الإنجليزية", grade_id=grade_11_science.id, total_mark=100)

        # الصف الثاني أدبي
        subject_11l_arabic = Subject(name="اللغة العربية", grade_id=grade_11_literary.id, total_mark=100)
        subject_11l_english = Subject(name="اللغة الإنجليزية", grade_id=grade_11_literary.id, total_mark=100)
        subject_11l_history = Subject(name="التاريخ", grade_id=grade_11_literary.id, total_mark=100)
        subject_11l_geography = Subject(name="الجغرافيا", grade_id=grade_11_literary.id, total_mark=100)
        subject_11l_philosophy = Subject(name="الفلسفة", grade_id=grade_11_literary.id, total_mark=100)

        # الصف الثالث علمي
        subject_12s_math = Subject(name="الرياضيات", grade_id=grade_12_science.id, total_mark=100)
        subject_12s_physics = Subject(name="الفيزياء", grade_id=grade_12_science.id, total_mark=100)
        subject_12s_chemistry = Subject(name="الكيمياء", grade_id=grade_12_science.id, total_mark=100)
        subject_12s_biology = Subject(name="الأحياء", grade_id=grade_12_science.id, total_mark=100)
        subject_12s_arabic = Subject(name="اللغة العربية", grade_id=grade_12_science.id, total_mark=100)
        subject_12s_english = Subject(name="اللغة الإنجليزية", grade_id=grade_12_science.id, total_mark=100)

        # الصف الثالث أدبي
        subject_12l_arabic = Subject(name="اللغة العربية", grade_id=grade_12_literary.id, total_mark=100)
        subject_12l_english = Subject(name="اللغة الإنجليزية", grade_id=grade_12_literary.id, total_mark=100)
        subject_12l_history = Subject(name="التاريخ", grade_id=grade_12_literary.id, total_mark=100)
        subject_12l_geography = Subject(name="الجغرافيا", grade_id=grade_12_literary.id, total_mark=100)
        subject_12l_philosophy = Subject(name="الفلسفة", grade_id=grade_12_literary.id, total_mark=100)

        db.session.add_all([
            subject_10_math, subject_10_physics, subject_10_chemistry, subject_10_biology, subject_10_arabic, subject_10_english,
            subject_11s_math, subject_11s_physics, subject_11s_chemistry, subject_11s_biology, subject_11s_arabic, subject_11s_english,
            subject_11l_arabic, subject_11l_english, subject_11l_history, subject_11l_geography, subject_11l_philosophy,
            subject_12s_math, subject_12s_physics, subject_12s_chemistry, subject_12s_biology, subject_12s_arabic, subject_12s_english,
            subject_12l_arabic, subject_12l_english, subject_12l_history, subject_12l_geography, subject_12l_philosophy
        ])
        db.session.commit()

        # إنشاء الفترات الدراسية
        period_1_1 = Period(name="الفصل الأول", academic_year="2023-2024")
        period_1_2 = Period(name="الفصل الثاني", academic_year="2023-2024")
        period_2_1 = Period(name="الفترة الأولى", academic_year="2023-2024")
        period_2_2 = Period(name="الفترة الثانية", academic_year="2023-2024")

        db.session.add_all([period_1_1, period_1_2, period_2_1, period_2_2])
        db.session.commit()

        # إنشاء بعض الطلاب النموذجيين
        students = [
            Student(name="أحمد علي", national_id="123456789012", class_id=class_10a.id),
            Student(name="محمد حسن", national_id="123456789013", class_id=class_10a.id),
            Student(name="فاطمة عبد الله", national_id="123456789014", class_id=class_10a.id),
            Student(name="علي محمود", national_id="123456789015", class_id=class_10a.id),
            Student(name="خالد عيسى", national_id="123456789016", class_id=class_10b.id),
            Student(name="مريم صالح", national_id="123456789017", class_id=class_10b.id),
            Student(name="يوسف أحمد", national_id="123456789018", class_id=class_10b.id),
            Student(name="ريم خالد", national_id="123456789019", class_id=class_10b.id),
            Student(name="سعيد محمد", national_id="123456789020", class_id=class_11_science.id),
            Student(name="ندى عادل", national_id="123456789021", class_id=class_11_science.id)
        ]

        db.session.add_all(students)
        db.session.commit()

def start_app():
    """تشغيل تطبيق Flask"""
    return app

def run_app():
    """تشغيل التطبيق كتطبيق سطح مكتب"""
    # تهيئة البيانات النموذجية
    initialize_sample_data()

    # إنشاء نافذة تطبيق سطح المكتب
    webview.create_window('نظام إدارة درجات الطلبة', start_app(), width=1200, height=800)
    webview.start(debug=True)

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--debug':
        # تشغيل التطبيق في وضع التصحيح
        app.run(debug=True)
    else:
        # تشغيل التطبيق كتطبيق سطح مكتب
        run_app()
