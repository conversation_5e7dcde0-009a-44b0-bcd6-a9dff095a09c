{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">توليد أرقام الجلوس</h1>
        <a href="{{ url_for('exam_prep') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-arrow-right ml-1"></i> العودة
        </a>
    </div>
    
    <!-- نموذج توليد أرقام الجلوس -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 class="text-lg font-semibold mb-4">إعدادات توليد أرقام الجلوس</h2>
        
        <form method="POST" action="{{ url_for('generate_seat_numbers') }}">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="grade-select" class="block text-gray-700 font-medium mb-2">الصف الدراسي</label>
                    <select id="grade-select" name="grade_id" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="" disabled selected>اختر الصف الدراسي</option>
                        {% for grade in grades %}
                        <option value="{{ grade.id }}">{{ grade.name }} - {{ grade.track }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="start-number" class="block text-gray-700 font-medium mb-2">رقم البداية</label>
                    <input type="number" id="start-number" name="start_number" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" value="1" min="1">
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors">
                    <i class="fas fa-cog ml-1"></i> توليد أرقام الجلوس
                </button>
            </div>
        </form>
    </div>
    
    <!-- جدول أرقام الجلوس -->
    {% if students %}
    <div>
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">أرقام الجلوس للطلاب</h2>
            <a href="{{ url_for('distribute_students') }}" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded transition-colors">
                <i class="fas fa-users ml-1"></i> توزيع الطلاب على القاعات
            </a>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="py-3 px-4 border-b text-right">#</th>
                        <th class="py-3 px-4 border-b text-right">الاسم</th>
                        <th class="py-3 px-4 border-b text-right">الرقم الوطني</th>
                        <th class="py-3 px-4 border-b text-right">الفصل</th>
                        <th class="py-3 px-4 border-b text-right">رقم الجلوس</th>
                    </tr>
                </thead>
                <tbody>
                    {% for student in students %}
                    <tr class="hover:bg-gray-50">
                        <td class="py-3 px-4 border-b">{{ loop.index }}</td>
                        <td class="py-3 px-4 border-b">{{ student.name }}</td>
                        <td class="py-3 px-4 border-b">{{ student.national_id }}</td>
                        <td class="py-3 px-4 border-b">{{ student.class_.name }}</td>
                        <td class="py-3 px-4 border-b">{{ student.seat_number }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
