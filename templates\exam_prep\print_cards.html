{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">طباعة مرايا الجلوس</h1>
        <a href="{{ url_for('exam_prep') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-arrow-right ml-1"></i> العودة
        </a>
    </div>
    
    <!-- نموذج طباعة مرايا الجلوس -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 class="text-lg font-semibold mb-4">إعدادات طباعة مرايا الجلوس</h2>
        
        <form id="print-form">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="grade-select" class="block text-gray-700 font-medium mb-2">الصف الدراسي</label>
                    <select id="grade-select" name="grade_id" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="" disabled selected>اختر الصف الدراسي</option>
                        {% for grade in grades %}
                        <option value="{{ grade.id }}">{{ grade.name }} - {{ grade.track }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="class-select" class="block text-gray-700 font-medium mb-2">الفصل (اختياري)</label>
                    <select id="class-select" name="class_id" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled>
                        <option value="" selected>جميع الفصول</option>
                    </select>
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="button" id="generate-btn" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors">
                    <i class="fas fa-cog ml-1"></i> عرض مرايا الجلوس
                </button>
            </div>
        </form>
    </div>
    
    <!-- مرايا الجلوس -->
    <div id="cards-container" class="hidden">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">مرايا الجلوس</h2>
            <button id="print-btn" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded transition-colors">
                <i class="fas fa-print ml-1"></i> طباعة
            </button>
        </div>
        
        <div id="seat-cards" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 print:grid-cols-2">
            <!-- سيتم ملء هذا القسم بواسطة JavaScript -->
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        
        #seat-cards, #seat-cards * {
            visibility: visible;
        }
        
        #seat-cards {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        
        .card {
            break-inside: avoid;
            page-break-inside: avoid;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    // تحديث قائمة الفصول عند اختيار الصف الدراسي
    document.getElementById('grade-select').addEventListener('change', function() {
        const gradeId = this.value;
        const classSelect = document.getElementById('class-select');
        
        if (gradeId) {
            // تفعيل قائمة الفصول
            classSelect.disabled = false;
            
            // طلب قائمة الفصول من الخادم
            fetch(`/api/classes?grade_id=${gradeId}`)
                .then(response => response.json())
                .then(data => {
                    // إعادة تعيين قائمة الفصول
                    classSelect.innerHTML = '<option value="" selected>جميع الفصول</option>';
                    
                    // إضافة الفصول إلى القائمة
                    data.classes.forEach(classObj => {
                        const option = document.createElement('option');
                        option.value = classObj.id;
                        option.textContent = classObj.name;
                        classSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء تحميل قائمة الفصول');
                });
        } else {
            // تعطيل قائمة الفصول
            classSelect.disabled = true;
            classSelect.innerHTML = '<option value="" selected>جميع الفصول</option>';
        }
    });
    
    // عرض مرايا الجلوس
    document.getElementById('generate-btn').addEventListener('click', function() {
        const gradeId = document.getElementById('grade-select').value;
        const classId = document.getElementById('class-select').value;
        
        if (!gradeId) {
            alert('يرجى اختيار الصف الدراسي');
            return;
        }
        
        // طلب بيانات الطلاب من الخادم
        let url = `/api/students?grade_id=${gradeId}`;
        if (classId) {
            url += `&class_id=${classId}`;
        }
        
        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (!data.students || data.students.length === 0) {
                    alert('لا يوجد طلاب مسجلين في هذا الصف الدراسي');
                    return;
                }
                
                // عرض مرايا الجلوس
                displaySeatCards(data.students);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء تحميل البيانات');
            });
    });
    
    // عرض مرايا الجلوس
    function displaySeatCards(students) {
        const cardsContainer = document.getElementById('seat-cards');
        cardsContainer.innerHTML = '';
        
        students.forEach(student => {
            const cardDiv = document.createElement('div');
            cardDiv.className = 'card bg-white border border-gray-300 rounded-lg p-4 shadow-sm';
            
            cardDiv.innerHTML = `
                <div class="text-center border-b border-gray-300 pb-2 mb-2">
                    <h3 class="text-lg font-bold">مرآة الجلوس</h3>
                </div>
                <div class="space-y-2">
                    <div class="flex">
                        <span class="font-semibold ml-2">الاسم:</span>
                        <span>${student.name}</span>
                    </div>
                    <div class="flex">
                        <span class="font-semibold ml-2">الرقم الوطني:</span>
                        <span>${student.national_id}</span>
                    </div>
                    <div class="flex">
                        <span class="font-semibold ml-2">الفصل:</span>
                        <span>${student.class_name}</span>
                    </div>
                    <div class="flex">
                        <span class="font-semibold ml-2">رقم الجلوس:</span>
                        <span class="font-bold">${student.seat_number || '-'}</span>
                    </div>
                    <div class="flex">
                        <span class="font-semibold ml-2">الرقم السري:</span>
                        <span class="font-bold">${student.secret_number || '-'}</span>
                    </div>
                </div>
            `;
            
            cardsContainer.appendChild(cardDiv);
        });
        
        document.getElementById('cards-container').classList.remove('hidden');
    }
    
    // طباعة مرايا الجلوس
    document.getElementById('print-btn').addEventListener('click', function() {
        window.print();
    });
</script>
{% endblock %}
