import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

def configure_app(app):
    """تكوين تطبيق Flask"""
    
    # تكوين السر
    app.secret_key = os.getenv('SECRET_KEY', 'default_secret_key_for_development')
    
    # تكوين قاعدة البيانات
    app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URI', 'sqlite:///student_grades.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # تكوين المجلدات الثابتة
    app.config['STATIC_FOLDER'] = 'static'
    app.config['TEMPLATE_FOLDER'] = 'templates'
    
    return app
