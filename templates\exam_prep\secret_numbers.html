{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">توليد الأرقام السرية</h1>
        <a href="{{ url_for('exam_prep') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-arrow-right ml-1"></i> العودة
        </a>
    </div>
    
    <!-- نموذج توليد الأرقام السرية -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 class="text-lg font-semibold mb-4">إعدادات توليد الأرقام السرية</h2>
        
        <form id="generate-form" method="POST" action="{{ url_for('generate_secret_numbers') }}">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="grade-select" class="block text-gray-700 font-medium mb-2">الصف الدراسي</label>
                    <select id="grade-select" name="grade_id" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="" disabled selected>اختر الصف الدراسي</option>
                        {% for grade in grades %}
                        <option value="{{ grade.id }}">{{ grade.name }} - {{ grade.track }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="pattern-select" class="block text-gray-700 font-medium mb-2">نمط الأرقام السرية</label>
                    <select id="pattern-select" name="pattern" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="random" selected>أرقام عشوائية</option>
                        <option value="sequential">أرقام متسلسلة</option>
                        <option value="prefix">بادئة + أرقام متسلسلة</option>
                    </select>
                </div>
                
                <div id="prefix-container" class="hidden">
                    <label for="prefix-input" class="block text-gray-700 font-medium mb-2">البادئة</label>
                    <input type="text" id="prefix-input" name="prefix" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="مثال: A، B، C">
                </div>
                
                <div>
                    <label for="length-input" class="block text-gray-700 font-medium mb-2">طول الرقم السري</label>
                    <input type="number" id="length-input" name="length" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" value="6" min="4" max="10">
                </div>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors">
                    <i class="fas fa-cog ml-1"></i> توليد الأرقام السرية
                </button>
            </div>
        </form>
    </div>
    
    <!-- جدول الأرقام السرية -->
    {% if students %}
    <div>
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">الأرقام السرية للطلاب</h2>
            <button id="save-btn" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded transition-colors">
                <i class="fas fa-save ml-1"></i> حفظ الأرقام السرية
            </button>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="py-3 px-4 border-b text-right">#</th>
                        <th class="py-3 px-4 border-b text-right">الاسم</th>
                        <th class="py-3 px-4 border-b text-right">الرقم الوطني</th>
                        <th class="py-3 px-4 border-b text-right">الفصل</th>
                        <th class="py-3 px-4 border-b text-right">الرقم السري</th>
                    </tr>
                </thead>
                <tbody>
                    {% for student in students %}
                    <tr class="hover:bg-gray-50">
                        <td class="py-3 px-4 border-b">{{ loop.index }}</td>
                        <td class="py-3 px-4 border-b">{{ student.name }}</td>
                        <td class="py-3 px-4 border-b">{{ student.national_id }}</td>
                        <td class="py-3 px-4 border-b">{{ student.class_.name }}</td>
                        <td class="py-3 px-4 border-b">
                            <input type="text" class="secret-number w-full border border-gray-300 rounded py-1 px-2" value="{{ student.secret_number }}" data-student-id="{{ student.id }}">
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
    // إظهار/إخفاء حقل البادئة
    document.getElementById('pattern-select').addEventListener('change', function() {
        const prefixContainer = document.getElementById('prefix-container');
        
        if (this.value === 'prefix') {
            prefixContainer.classList.remove('hidden');
        } else {
            prefixContainer.classList.add('hidden');
        }
    });
    
    {% if students %}
    // حفظ الأرقام السرية
    document.getElementById('save-btn').addEventListener('click', function() {
        const secretNumbers = [];
        
        document.querySelectorAll('.secret-number').forEach(input => {
            secretNumbers.push({
                student_id: input.dataset.studentId,
                secret_number: input.value
            });
        });
        
        fetch('/exam-prep/save-secret-numbers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                secret_numbers: secretNumbers
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حفظ الأرقام السرية بنجاح');
            } else {
                alert('حدث خطأ أثناء حفظ الأرقام السرية');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حفظ الأرقام السرية');
        });
    });
    {% endif %}
</script>
{% endblock %}
