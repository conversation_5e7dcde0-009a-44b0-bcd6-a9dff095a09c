{% extends 'base.html' %}

{% block title %}إدارة المواد الدراسية - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}إدارة المواد الدراسية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>ملاحظة:</strong> هذه الصفحة تعرض المواد الدراسية الأساسية. لإدارة المواد حسب المرحلة الدراسية وتحديد الدرجات وطرق التقييم المختلفة، يرجى الانتقال إلى
            <a href="{{ url_for('stage_subjects.index') }}" class="alert-link">إدارة المواد حسب المرحلة</a>.
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12 text-end">
        <a href="{{ url_for('subjects.add') }}" class="btn btn-success">
            <i class="fas fa-plus me-1"></i>
            إضافة مادة جديدة
        </a>
        <a href="{{ url_for('stage_subjects.index') }}" class="btn btn-primary">
            <i class="fas fa-book me-1"></i>
            إدارة المواد حسب المرحلة
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-book me-2"></i>
            قائمة المواد الدراسية الأساسية
        </h5>
    </div>
    <div class="card-body">
        {% if subjects %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم المادة</th>
                        <th>الرمز</th>
                        <th>الوصف</th>
                        <th>عدد المراحل</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for subject in subjects %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ subject.name }}</td>
                        <td>{{ subject.code or '-' }}</td>
                        <td>{{ subject.description|truncate(50) or '-' }}</td>
                        <td>
                            <span class="badge bg-info">{{ subject.stage_subjects|length }} مرحلة</span>
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('subjects.edit', subject_id=subject.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ url_for('subjects.delete', subject_id=subject.id) }}" method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف المادة: {{ subject.name }}؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                    <button type="submit" class="btn btn-sm btn-danger" title="حذف" {% if subject.stage_subjects %}disabled{% endif %}>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            لا توجد مواد دراسية مسجلة حالياً.
            <a href="{{ url_for('subjects.add') }}" class="alert-link">إضافة مادة جديدة</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
