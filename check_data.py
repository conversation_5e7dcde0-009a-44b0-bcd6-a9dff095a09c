from flask import Flask
from models import db, Grade, Class, Subject, Period, Student
from run import app

def check_database():
    """التحقق من وجود بيانات في قاعدة البيانات"""
    
    print("بدء التحقق من البيانات...")
    
    # التحقق من الصفوف الدراسية
    grades = Grade.query.all()
    print(f"عدد الصفوف الدراسية: {len(grades)}")
    for grade in grades:
        print(f"- {grade.name} ({grade.track})")
    
    # التحقق من الفصول
    classes = Class.query.all()
    print(f"\nعدد الفصول: {len(classes)}")
    for class_ in classes:
        print(f"- {class_.name} (الصف الدراسي: {class_.grade.name})")
    
    # التحقق من المواد الدراسية
    subjects = Subject.query.all()
    print(f"\nعدد المواد الدراسية: {len(subjects)}")
    for subject in subjects:
        print(f"- {subject.name} (الصف الدراسي: {subject.grade.name}, الدرجة الكلية: {subject.total_mark})")
    
    # التحقق من الفترات الدراسية
    periods = Period.query.all()
    print(f"\nعدد الفترات الدراسية: {len(periods)}")
    for period in periods:
        print(f"- {period.name} ({period.academic_year}, معتمدة: {'نعم' if period.is_approved else 'لا'})")
    
    # التحقق من الطلاب
    students = Student.query.all()
    print(f"\nعدد الطلاب: {len(students)}")
    for student in students:
        print(f"- {student.name} (الفصل: {student.class_.name})")
    
    print("\nتم التحقق من البيانات بنجاح.")

if __name__ == "__main__":
    with app.app_context():
        check_database()
