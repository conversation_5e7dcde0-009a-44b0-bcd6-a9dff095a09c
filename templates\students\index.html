{% extends 'base.html' %}

{% block title %}إدارة الطلاب - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}إدارة الطلاب{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <form action="{{ url_for('students.search') }}" method="get" class="d-flex">
            <div class="input-group">
                <select name="type" class="form-select" style="max-width: 150px;">
                    <option value="name" {% if search_type == 'name' %}selected{% endif %}>الاسم</option>
                    <option value="national_id" {% if search_type == 'national_id' %}selected{% endif %}>الرقم الوطني</option>
                </select>
                <input type="text" name="query" class="form-control" placeholder="بحث..." value="{{ query or '' }}">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="{{ url_for('students.add') }}" class="btn btn-success">
            <i class="fas fa-plus me-1"></i>
            إضافة طالب جديد
        </a>
    </div>
</div>

{% if students %}
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="dataTable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم الكامل</th>
                        <th>الرقم الوطني</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for student in students %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ student.full_name }}</td>
                        <td>{{ student.national_id }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('grades.student_grades', student_id=student.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="عرض الدرجات">
                                    <i class="fas fa-chart-bar"></i>
                                </a>
                                <a href="{{ url_for('students.edit', student_id=student.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ url_for('students.delete', student_id=student.id) }}" method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف الطالب: {{ student.full_name }}؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                    <button type="submit" class="btn btn-sm btn-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i>
    لا يوجد طلاب مسجلين حالياً.
    <a href="{{ url_for('students.add') }}" class="alert-link">إضافة طالب جديد</a>
</div>
{% endif %}
{% endblock %}
