/* ===== الخطوط ===== */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* ===== الإعدادات العامة ===== */
body {
    font-family: 'Tajawal', sans-serif;
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    max-width: 1200px;
}

.footer {
    margin-top: auto;
}

/* ===== العناوين ===== */
.page-title {
    color: #343a40;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* ===== البطاقات ===== */
.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-title {
    font-weight: 700;
}

/* ===== الأزرار ===== */
.btn {
    border-radius: 5px;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

/* ===== الجداول ===== */
.table {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table thead {
    background-color: #007bff;
    color: #fff;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* ===== النماذج ===== */
.form-control {
    border-radius: 5px;
    padding: 10px 15px;
    border: 1px solid #ced4da;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
}

/* ===== التنبيهات ===== */
.alert {
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* ===== شريط التنقل ===== */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    padding: 8px 16px;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

/* ===== الأيقونات ===== */
.fa-4x {
    margin-bottom: 15px;
}

/* ===== الصفحات المحددة ===== */

/* صفحة الطلاب */
.student-card {
    border-right: 4px solid #007bff;
}

/* صفحة الدرجات */
.grade-passed {
    background-color: rgba(40, 167, 69, 0.1);
}

.grade-failed {
    background-color: rgba(220, 53, 69, 0.1);
}

/* صفحة التقارير */
.report-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #007bff;
}

/* ===== تخصيص للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .card {
        margin-bottom: 20px;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }
}
