{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">تعديل المادة</h1>
        <a href="{{ url_for('list_subjects', grade_id=subject.grade_id) }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-arrow-right ml-1"></i> العودة
        </a>
    </div>
    
    <form method="POST" action="{{ url_for('edit_subject', id=subject.id) }}">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
                <label for="name" class="block text-gray-700 font-medium mb-2">اسم المادة</label>
                <input type="text" id="name" name="name" value="{{ subject.name }}" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
            </div>
            
            <div>
                <label for="grade_id" class="block text-gray-700 font-medium mb-2">الصف الدراسي</label>
                <select id="grade_id" name="grade_id" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                    {% for grade in grades %}
                    <option value="{{ grade.id }}" {% if grade.id == subject.grade_id %}selected{% endif %}>{{ grade.name }} - {{ grade.track }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div>
                <label for="total_mark" class="block text-gray-700 font-medium mb-2">الدرجة الكلية</label>
                <input type="number" id="total_mark" name="total_mark" value="{{ subject.total_mark }}" min="1" max="1000" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
            </div>
        </div>
        
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors">
                <i class="fas fa-save ml-1"></i> حفظ التغييرات
            </button>
        </div>
    </form>
</div>
{% endblock %}
