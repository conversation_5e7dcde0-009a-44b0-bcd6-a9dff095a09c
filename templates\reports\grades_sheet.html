<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كشف درجات - {{ subject.name }}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <style>
        body {
            font-family: 'Tahoma', sans-serif;
        }
        .report-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .report-header h4, .report-header h5, .report-header h6 {
            margin: 5px 0;
        }
        .table {
            border: 2px solid #000 !important;
        }
        .table th, .table td {
            border: 1px solid #000 !important;
            text-align: center;
            vertical-align: middle;
            padding: 0.4rem;
        }
        .table thead th {
            background-color: #e9ecef !important;
            font-weight: bold;
        }
        .signatures {
            margin-top: 50px;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        .signatures div {
            width: 200px;
        }
        .signatures p {
            margin-top: 40px;
            border-top: 1px solid #000;
            padding-top: 5px;
        }

        @media print {
            .no-print {
                display: none;
            }
            body {
                -webkit-print-color-adjust: exact !important; /* Chrome, Safari */
                color-adjust: exact !important; /* Firefox */
            }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="text-center mb-4 no-print">
            <button onclick="window.print()" class="btn btn-primary">طباعة الكشف</button>
            <a href="{{ url_for('reports') }}" class="btn btn-secondary">العودة للتقارير</a>
        </div>

        <div class="report-header">
            <h4>بسم الله الرحمن الرحيم</h4>
            <h5>الدولة الليبية - وزارة التربية والتعليم</h5>
            <h6>كشف درجات مادة: {{ subject.name }}</h6>
            <h6>للفصل: {{ class_obj.name }} - العام الدراسي: {{ period.academic_year }}</h6>
            <h6>الفترة: {{ period.name }}</h6>
        </div>

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>#</th>
                    <th>اسم الطالب</th>
                    <th>الرقم الوطني</th>
                    <th>أعمال الفصل</th>
                    <th>الامتحان</th>
                    <th>المجموع</th>
                    <th>النتيجة</th>
                    <th>ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                {% for student_data in students %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td class="text-end" style="min-width: 200px;">{{ student_data.name }}</td>
                    <td>{{ student_data.national_id }}</td>
                    <td>{{ student_data.classwork_mark if student_data.classwork_mark is not none else '-' }}</td>
                    <td>{{ student_data.exam_mark if student_data.exam_mark is not none else '-' }}</td>
                    <td><strong>{{ student_data.total_mark if student_data.total_mark is not none else '-' }}</strong></td>
                    <td>
                        {% if student_data.is_passed is not none %}
                            {% if student_data.is_passed %}
                                <span class="text-success">ناجح</span>
                            {% else %}
                                <span class="text-danger">راسب</span>
                            {% endif %}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td style="min-width: 150px;"></td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="signatures">
            <div>
                <p>أستاذ المادة</p>
            </div>
            <div>
                <p>رئيس قسم الدراسة والامتحانات</p>
            </div>
            <div>
                <p>مدير المدرسة</p>
            </div>
        </div>
    </div>
</body>
</html>