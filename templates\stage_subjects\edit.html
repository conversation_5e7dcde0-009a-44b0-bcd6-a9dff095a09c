{% extends 'base.html' %}

{% block title %}تعديل إعدادات المادة - {{ stage_subject.subject.name }} - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}تعديل إعدادات المادة - {{ stage_subject.subject.name }} ({{ stage_subject.stage.name }}){% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <span>تعديل إعدادات المادة</span>
                <a href="{{ url_for('stage_subjects.stage_subjects', stage_id=stage_subject.stage_id) }}" class="btn btn-sm btn-light">
                    <i class="fas fa-arrow-right me-1"></i>
                    رجوع
                </a>
            </div>
            <div class="card-body">
                <form action="{{ url_for('stage_subjects.edit', stage_subject_id=stage_subject.id) }}" method="post">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">المادة الدراسية</label>
                            <input type="text" class="form-control" value="{{ stage_subject.subject.name }}" readonly>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">المرحلة الدراسية</label>
                            <input type="text" class="form-control" value="{{ stage_subject.stage.name }}" readonly>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="total_mark" class="form-label">الدرجة الكلية <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="total_mark" name="total_mark" min="1" step="0.5" value="{{ stage_subject.total_mark }}" required>
                        </div>

                        <div class="col-md-6">
                            <label for="passing_percentage" class="form-label">نسبة النجاح (%) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="passing_percentage" name="passing_percentage" min="0" max="100" step="0.1" value="{{ stage_subject.passing_percentage }}" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="classwork_percentage" class="form-label">نسبة أعمال الفصل (%) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="classwork_percentage" name="classwork_percentage" min="0" max="100" step="0.1" value="{{ stage_subject.classwork_percentage }}" required>
                        </div>

                        <div class="col-md-6">
                            <label for="exam_percentage" class="form-label">نسبة الامتحان (%) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="exam_percentage" name="exam_percentage" min="0" max="100" step="0.1" value="{{ stage_subject.exam_percentage }}" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="min_exam_percentage" class="form-label">الحد الأدنى لنسبة النجاح في الامتحان (%) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="min_exam_percentage" name="min_exam_percentage" min="0" max="100" step="0.1" value="{{ stage_subject.min_exam_percentage }}" required>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="daily_evaluation_only" name="daily_evaluation_only" {% if stage_subject.daily_evaluation_only %}checked{% endif %}>
                        <label class="form-check-label" for="daily_evaluation_only">تقييم يومي فقط (للصفوف 1-3)</label>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {% if stage_subject.is_active %}checked{% endif %}>
                        <label class="form-check-label" for="is_active">المادة مفعلة</label>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> مجموع نسبة أعمال الفصل ونسبة الامتحان يجب أن يساوي 100%.
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('stage_subjects.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            رجوع
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener("DOMContentLoaded", function() {
        // التحقق من مجموع النسب المئوية
        var classworkPercentage = document.getElementById("classwork_percentage");
        var examPercentage = document.getElementById("exam_percentage");

        function updatePercentages() {
            var classwork = parseFloat(classworkPercentage.value) || 0;
            var exam = parseFloat(examPercentage.value) || 0;
            var total = classwork + exam;

            if (total != 100) {
                classworkPercentage.classList.add("is-invalid");
                examPercentage.classList.add("is-invalid");
            } else {
                classworkPercentage.classList.remove("is-invalid");
                examPercentage.classList.remove("is-invalid");
            }
        }

        classworkPercentage.addEventListener("input", function() {
            var classwork = parseFloat(classworkPercentage.value) || 0;
            examPercentage.value = (100 - classwork).toFixed(1);
            updatePercentages();
        });

        examPercentage.addEventListener("input", function() {
            var exam = parseFloat(examPercentage.value) || 0;
            classworkPercentage.value = (100 - exam).toFixed(1);
            updatePercentages();
        });
    });
</script>
{% endblock %}
