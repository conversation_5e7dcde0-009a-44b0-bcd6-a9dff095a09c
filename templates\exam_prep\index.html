{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <h1 class="text-2xl font-bold mb-6 text-blue-600">التجهيز للامتحانات</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- بطاقة توليد الأرقام السرية -->
        <div class="bg-blue-50 rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center mb-4">
                <div class="bg-blue-500 text-white p-3 rounded-full mr-4">
                    <i class="fas fa-key text-xl"></i>
                </div>
                <h2 class="text-xl font-semibold">توليد الأرقام السرية</h2>
            </div>
            <p class="text-gray-600 mb-4">توليد أرقام سرية للطلاب بشكل عشوائي أو وفق نمط محدد.</p>
            <a href="{{ url_for('generate_secret_numbers') }}" class="block text-center bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition-colors">
                الذهاب إلى توليد الأرقام السرية
            </a>
        </div>
        
        <!-- بطاقة توليد أرقام الجلوس -->
        <div class="bg-green-50 rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center mb-4">
                <div class="bg-green-500 text-white p-3 rounded-full mr-4">
                    <i class="fas fa-chair text-xl"></i>
                </div>
                <h2 class="text-xl font-semibold">توليد أرقام الجلوس</h2>
            </div>
            <p class="text-gray-600 mb-4">توليد أرقام جلوس للطلاب وتوزيعهم على القاعات.</p>
            <a href="{{ url_for('generate_seat_numbers') }}" class="block text-center bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded transition-colors">
                الذهاب إلى توليد أرقام الجلوس
            </a>
        </div>
        
        <!-- بطاقة توزيع الطلاب على القاعات -->
        <div class="bg-purple-50 rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center mb-4">
                <div class="bg-purple-500 text-white p-3 rounded-full mr-4">
                    <i class="fas fa-chalkboard text-xl"></i>
                </div>
                <h2 class="text-xl font-semibold">توزيع الطلاب على القاعات</h2>
            </div>
            <p class="text-gray-600 mb-4">توزيع الطلاب على القاعات حسب أرقام الجلوس.</p>
            <a href="{{ url_for('distribute_students') }}" class="block text-center bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 rounded transition-colors">
                الذهاب إلى توزيع الطلاب
            </a>
        </div>
        
        <!-- بطاقة طباعة مرايا الجلوس -->
        <div class="bg-yellow-50 rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div class="flex items-center mb-4">
                <div class="bg-yellow-500 text-white p-3 rounded-full mr-4">
                    <i class="fas fa-print text-xl"></i>
                </div>
                <h2 class="text-xl font-semibold">طباعة مرايا الجلوس</h2>
            </div>
            <p class="text-gray-600 mb-4">طباعة مرايا الجلوس (كروت الجلوس) للطلاب.</p>
            <a href="{{ url_for('print_seat_cards') }}" class="block text-center bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-2 px-4 rounded transition-colors">
                الذهاب إلى طباعة مرايا الجلوس
            </a>
        </div>
    </div>
</div>
{% endblock %}
