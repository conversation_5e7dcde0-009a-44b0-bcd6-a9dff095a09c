from app_config import app
from models import db, Student, Subject, Period, StudentGrade, Class

with app.app_context():
    try:
        # اختبار الاتصال بقاعدة البيانات
        print("Testing database connection...")
        
        # عدد الطلاب
        student_count = Student.query.count()
        print(f"Total students: {student_count}")
        
        # عدد المواد
        subject_count = Subject.query.count()
        print(f"Total subjects: {subject_count}")
        
        # عدد الفترات
        period_count = Period.query.count()
        print(f"Total periods: {period_count}")
        
        # عدد الدرجات
        grade_count = StudentGrade.query.count()
        print(f"Total grades: {grade_count}")
        
        # عدد الفصول
        class_count = Class.query.count()
        print(f"Total classes: {class_count}")
        
        # اختبار الفصل رقم 1
        class_1 = Class.query.get(1)
        if class_1:
            print(f"Class 1 found: {class_1.name}")
            students_in_class = Student.query.filter_by(class_id=1).count()
            print(f"Students in class 1: {students_in_class}")
        else:
            print("Class 1 not found")
        
        # اختبار الفترات
        periods = Period.query.all()
        print("\nPeriods:")
        for period in periods:
            print(f"- {period.id}: {period.name}")
        
        print("\nDatabase connection test completed successfully!")
        
    except Exception as e:
        print(f"Database error: {e}")
        import traceback
        traceback.print_exc()